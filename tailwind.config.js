/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'primary': {
          DEFAULT: '#0F4C81', // Moroccan Blue
          50: '#E6EEF4',
          100: '#CCDDE9',
          200: '#99BBD4',
          300: '#6699BE',
          400: '#3377A9',
          500: '#0F4C81', // Primary
          600: '#0D4373',
          700: '#0B3A65',
          800: '#093057',
          900: '#072749',
        },
        'secondary': {
          DEFAULT: '#2D8B7D', // Moroccan Green
          50: '#E9F5F3',
          100: '#D3EBE7',
          200: '#A7D7CF',
          300: '#7BC3B7',
          400: '#4FAF9F',
          500: '#2D8B7D', // Primary
          600: '#297D71',
          700: '#256F65',
          800: '#216159',
          900: '#1D534D',
        },
        'accent': {
          DEFAULT: '#D4AF37', // Moroccan Gold
          50: '#FAF5E6',
          100: '#F5EBCC',
          200: '#EBD799',
          300: '#E1C366',
          400: '#D7AF33',
          500: '#D4AF37', // Primary
          600: '#BF9E32',
          700: '#A98C2C',
          800: '#937B27',
          900: '#7D6921',
        },
      },
      fontFamily: {
        sans: [
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica Neue',
          'sans-serif',
        ],
      },
    },
  },
  plugins: [],
};