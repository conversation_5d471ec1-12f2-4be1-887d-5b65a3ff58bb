import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ewfawatuempiddkwxjyj.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.zqtvb_Vv7REbIdGPvMH603jsbT1KMnIb2XKIOEyEBcc';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabase() {
  try {
    console.log('🔍 Checking Supabase database structure...\n');

    // Check if ERP tables exist by trying to query them
    const erpTables = [
      'tenants', 'billing_plans', 'tenant_subscriptions', 'users', 'user_roles',
      'companies', 'fiscal_years', 'currencies', 'tax_rates', 'chart_of_accounts',
      'customers', 'suppliers', 'products_services', 'invoices', 'invoice_items',
      'expenses', 'expense_categories', 'payments', 'bank_accounts', 'reports',
      'audit_logs', 'notifications', 'settings'
    ];

    console.log('🏢 ERP Tables Status:');
    const tableStatus = {};

    for (const table of erpTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          console.log(`  ❌ ${table} - ${error.message}`);
          tableStatus[table] = false;
        } else {
          console.log(`  ✅ ${table}`);
          tableStatus[table] = true;
        }
      } catch (err) {
        console.log(`  ❌ ${table} - ${err.message}`);
        tableStatus[table] = false;
      }
    }



    // Check sample data for existing tables
    console.log('\n📊 Sample Data:');

    if (tableStatus.tenants) {
      const { data: tenantsData } = await supabase
        .from('tenants')
        .select('*')
        .limit(5);
      console.log(`  Tenants: ${tenantsData?.length || 0} records`);

      if (tenantsData && tenantsData.length > 0) {
        console.log('    Sample tenant:', tenantsData[0].name);
      }
    }

    if (tableStatus.users) {
      const { data: usersData } = await supabase
        .from('users')
        .select('*')
        .limit(5);
      console.log(`  Users: ${usersData?.length || 0} records`);
    }

    if (tableStatus.invoices) {
      const { data: invoicesData } = await supabase
        .from('invoices')
        .select('*')
        .limit(5);
      console.log(`  Invoices: ${invoicesData?.length || 0} records`);
    }

    if (tableStatus.expenses) {
      const { data: expensesData } = await supabase
        .from('expenses')
        .select('*')
        .limit(5);
      console.log(`  Expenses: ${expensesData?.length || 0} records`);
    }

    if (tableStatus.billing_plans) {
      const { data: plansData } = await supabase
        .from('billing_plans')
        .select('*');
      console.log(`  Billing Plans: ${plansData?.length || 0} records`);

      if (plansData && plansData.length > 0) {
        console.log('\n💰 Available Billing Plans:');
        plansData.forEach(plan => {
          console.log(`  - ${plan.name} (${plan.slug}): ${plan.price_monthly} MAD/month`);
        });
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

checkDatabase();
