import { useState, useCallback, useEffect } from 'react';
import { ValidationRule, validateForm, ValidationResult } from '../utils/validation';

interface FormField {
  value: any;
  rules: ValidationRule;
  label: string;
  touched?: boolean;
}

interface UseFormValidationOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
}

interface UseFormValidationReturn<T> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, error: string) => void;
  clearError: (field: keyof T) => void;
  clearErrors: () => void;
  setTouched: (field: keyof T, touched?: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  validateField: (field: keyof T) => string | null;
  validateForm: () => ValidationResult;
  handleSubmit: (onSubmit: (values: T) => void | Promise<void>) => (e?: React.FormEvent) => Promise<void>;
  reset: (initialValues?: Partial<T>) => void;
}

export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationSchema: Record<keyof T, ValidationRule>,
  options: UseFormValidationOptions = {}
): UseFormValidationReturn<T> {
  const {
    validateOnChange = false,
    validateOnBlur = true,
    validateOnSubmit = true,
  } = options;

  const [values, setValuesState] = useState<T>(initialValues);
  const [errors, setErrorsState] = useState<Record<string, string>>({});
  const [touched, setTouchedState] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create form fields for validation
  const createFormFields = useCallback((currentValues: T): Record<string, FormField> => {
    const fields: Record<string, FormField> = {};
    
    Object.entries(validationSchema).forEach(([fieldName, rules]) => {
      fields[fieldName] = {
        value: currentValues[fieldName as keyof T],
        rules,
        label: fieldName.charAt(0).toUpperCase() + fieldName.slice(1).replace(/([A-Z])/g, ' $1'),
        touched: touched[fieldName],
      };
    });
    
    return fields;
  }, [validationSchema, touched]);

  // Validate a single field
  const validateField = useCallback((field: keyof T): string | null => {
    const fieldName = String(field);
    const rules = validationSchema[field];
    const value = values[field];
    const label = fieldName.charAt(0).toUpperCase() + fieldName.slice(1).replace(/([A-Z])/g, ' $1');
    
    if (!rules) return null;
    
    const { validateField: validateSingleField } = require('../utils/validation');
    return validateSingleField(value, rules, label);
  }, [values, validationSchema]);

  // Validate entire form
  const validateFormFields = useCallback((): ValidationResult => {
    const fields = createFormFields(values);
    return validateForm(fields);
  }, [values, createFormFields]);

  // Set a single value
  const setValue = useCallback((field: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [field]: value }));
    
    // Clear error when value changes
    if (errors[String(field)]) {
      setErrorsState(prev => {
        const newErrors = { ...prev };
        delete newErrors[String(field)];
        return newErrors;
      });
    }
    
    // Validate on change if enabled
    if (validateOnChange && touched[String(field)]) {
      const fieldError = validateField(field);
      if (fieldError) {
        setErrorsState(prev => ({ ...prev, [String(field)]: fieldError }));
      }
    }
  }, [errors, touched, validateOnChange, validateField]);

  // Set multiple values
  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }));
  }, []);

  // Set a field error
  const setError = useCallback((field: keyof T, error: string) => {
    setErrorsState(prev => ({ ...prev, [String(field)]: error }));
  }, []);

  // Clear a field error
  const clearError = useCallback((field: keyof T) => {
    setErrorsState(prev => {
      const newErrors = { ...prev };
      delete newErrors[String(field)];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrorsState({});
  }, []);

  // Set field touched state
  const setTouched = useCallback((field: keyof T, touchedValue: boolean = true) => {
    setTouchedState(prev => ({ ...prev, [String(field)]: touchedValue }));
    
    // Validate on blur if enabled
    if (touchedValue && validateOnBlur) {
      const fieldError = validateField(field);
      if (fieldError) {
        setErrorsState(prev => ({ ...prev, [String(field)]: fieldError }));
      }
    }
  }, [validateOnBlur, validateField]);

  // Set submitting state
  const setSubmitting = useCallback((submitting: boolean) => {
    setIsSubmitting(submitting);
  }, []);

  // Handle form submission
  const handleSubmit = useCallback((onSubmit: (values: T) => void | Promise<void>) => {
    return async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }
      
      setIsSubmitting(true);
      
      try {
        // Validate form if enabled
        if (validateOnSubmit) {
          const validation = validateFormFields();
          setErrorsState(validation.errors);
          
          if (!validation.isValid) {
            // Mark all fields as touched to show errors
            const allTouched: Record<string, boolean> = {};
            Object.keys(validationSchema).forEach(field => {
              allTouched[field] = true;
            });
            setTouchedState(allTouched);
            return;
          }
        }
        
        // Call the submit handler
        await onSubmit(values);
      } catch (error) {
        console.error('Form submission error:', error);
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [values, validateOnSubmit, validateFormFields, validationSchema]);

  // Reset form
  const reset = useCallback((newInitialValues?: Partial<T>) => {
    const resetValues = newInitialValues ? { ...initialValues, ...newInitialValues } : initialValues;
    setValuesState(resetValues);
    setErrorsState({});
    setTouchedState({});
    setIsSubmitting(false);
  }, [initialValues]);

  // Calculate if form is valid
  const isValid = Object.keys(errors).length === 0;

  // Auto-validate on mount if any fields are pre-filled
  useEffect(() => {
    const hasPrefilledValues = Object.values(values).some(value => 
      value !== '' && value !== null && value !== undefined
    );
    
    if (hasPrefilledValues && validateOnChange) {
      const validation = validateFormFields();
      setErrorsState(validation.errors);
    }
  }, []); // Only run on mount

  return {
    values,
    errors,
    touched,
    isValid,
    isSubmitting,
    setValue,
    setValues,
    setError,
    clearError,
    clearErrors,
    setTouched,
    setSubmitting,
    validateField,
    validateForm: validateFormFields,
    handleSubmit,
    reset,
  };
}

// Helper hook for common form patterns
export function useSimpleForm<T extends Record<string, any>>(
  initialValues: T,
  onSubmit: (values: T) => void | Promise<void>
) {
  const [values, setValues] = useState<T>(initialValues);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [values, onSubmit]);

  const reset = useCallback(() => {
    setValues(initialValues);
    setIsSubmitting(false);
  }, [initialValues]);

  return {
    values,
    setValue,
    setValues,
    isSubmitting,
    handleSubmit,
    reset,
  };
}
