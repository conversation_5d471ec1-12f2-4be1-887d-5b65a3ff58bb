import { useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';

export interface AuthUser extends User {
  tenant_id?: string | null;
  role?: string | null;
  first_name?: string | null;
  last_name?: string | null;
}

interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
}

// Demo user for testing
const createDemoUser = (email: string): AuthUser => ({
  id: 'demo-user-id',
  email,
  aud: 'authenticated',
  role: 'authenticated',
  email_confirmed_at: new Date().toISOString(),
  phone: '',
  confirmed_at: new Date().toISOString(),
  last_sign_in_at: new Date().toISOString(),
  app_metadata: {},
  user_metadata: {},
  identities: [],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  tenant_id: 'demo-tenant-id',
  role: 'admin',
  first_name: 'Demo',
  last_name: 'User',
});

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: false,
  });

  console.log("useAuth hook - current state:", authState);

  useEffect(() => {
    // Check for existing demo session
    try {
      const savedUser = localStorage.getItem('demo_user');
      if (savedUser) {
        const user = JSON.parse(savedUser);
        console.log("Found saved demo user:", user);
        setAuthState({
          user,
          session: null,
          loading: false,
        });
      }
    } catch (e) {
      console.log("No saved session found");
    }
  }, []);

  const signIn = async (email: string, password: string) => {
    console.log("Demo signIn called with:", email, password);
    
    // Demo authentication - accept demo credentials or any email/password
    if ((email === '<EMAIL>' && password === 'demo123') || 
        (email && password && password.length >= 6)) {
      
      const demoUser = createDemoUser(email);
      
      // Save to localStorage
      localStorage.setItem('demo_user', JSON.stringify(demoUser));
      
      setAuthState({
        user: demoUser,
        session: null,
        loading: false,
      });
      
      return { data: { user: demoUser, session: null }, error: null };
    } else {
      return { 
        data: { user: null, session: null }, 
        error: { message: 'Invalid credentials' } 
      };
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    console.log("Demo signUp called with:", email, password, metadata);
    
    if (email && password && password.length >= 6) {
      const demoUser = createDemoUser(email);
      
      // Save to localStorage
      localStorage.setItem('demo_user', JSON.stringify(demoUser));
      
      setAuthState({
        user: demoUser,
        session: null,
        loading: false,
      });
      
      return { data: { user: demoUser, session: null }, error: null };
    } else {
      return { 
        data: { user: null, session: null }, 
        error: { message: 'Invalid signup data' } 
      };
    }
  };

  const signOut = async () => {
    console.log("Demo signOut called");
    
    // Clear localStorage
    localStorage.removeItem('demo_user');
    
    setAuthState({
      user: null,
      session: null,
      loading: false,
    });
    
    return { error: null };
  };

  const resetPassword = async (email: string) => {
    console.log("Demo resetPassword called with:", email);
    return { 
      data: { user: null, session: null }, 
      error: null 
    };
  };

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
};
