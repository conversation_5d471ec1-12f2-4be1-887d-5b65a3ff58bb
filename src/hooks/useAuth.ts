import { useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';

interface AuthUser extends User {
  tenant_id?: string;
  role?: string;
  first_name?: string;
  last_name?: string;
}

interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
  });

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        // Get user profile data
        const { data: userProfile } = await supabase
          .from('users')
          .select(`
            *,
            user_roles (
              role,
              is_active
            )
          `)
          .eq('id', session.user.id)
          .single();

        const enhancedUser: AuthUser = {
          ...session.user,
          tenant_id: userProfile?.tenant_id,
          role: userProfile?.user_roles?.[0]?.role,
          first_name: userProfile?.first_name,
          last_name: userProfile?.last_name,
        };

        setAuthState({
          user: enhancedUser,
          session,
          loading: false,
        });
      } else {
        setAuthState({
          user: null,
          session: null,
          loading: false,
        });
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          // Get user profile data
          const { data: userProfile } = await supabase
            .from('users')
            .select(`
              *,
              user_roles (
                role,
                is_active
              )
            `)
            .eq('id', session.user.id)
            .single();

          const enhancedUser: AuthUser = {
            ...session.user,
            tenant_id: userProfile?.tenant_id,
            role: userProfile?.user_roles?.[0]?.role,
            first_name: userProfile?.first_name,
            last_name: userProfile?.last_name,
          };

          setAuthState({
            user: enhancedUser,
            session,
            loading: false,
          });
        } else {
          setAuthState({
            user: null,
            session: null,
            loading: false,
          });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email);
    return { data, error };
  };

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
};