import { useState, useEffect, useCallback, useRef } from "react";
import { User, Session } from "@supabase/supabase-js";
import { supabase } from "../lib/supabase";

interface AuthUser extends User {
  tenant_id?: string;
  role?: string;
  first_name?: string;
  last_name?: string;
}

interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  isSessionExpired: boolean;
  lastActivity: Date | null;
  sessionTimeoutWarning: boolean;
}

// Session management constants
const SESSION_TIMEOUT_WARNING = 5 * 60 * 1000; // 5 minutes before expiry
const ACTIVITY_CHECK_INTERVAL = 30 * 1000; // Check every 30 seconds
const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes of inactivity

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    isSessionExpired: false,
    lastActivity: null,
    sessionTimeoutWarning: false,
  });

  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const activityCheckRef = useRef<NodeJS.Timeout | null>(null);

  // Update last activity timestamp
  const updateActivity = useCallback(() => {
    const now = new Date();
    setAuthState((prev) => ({ ...prev, lastActivity: now }));

    // Store in localStorage for cross-tab synchronization
    localStorage.setItem("lastActivity", now.toISOString());
  }, []);

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (activityTimeoutRef.current) {
      clearTimeout(activityTimeoutRef.current);
      activityTimeoutRef.current = null;
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
      warningTimeoutRef.current = null;
    }
    if (activityCheckRef.current) {
      clearTimeout(activityCheckRef.current);
      activityCheckRef.current = null;
    }
  }, []);

  // Handle session timeout
  const handleSessionTimeout = useCallback(async () => {
    setAuthState((prev) => ({
      ...prev,
      isSessionExpired: true,
      sessionTimeoutWarning: false,
    }));

    // Sign out the user
    await supabase.auth.signOut();

    // Clear stored activity
    localStorage.removeItem("lastActivity");

    // Show timeout message
    console.log("Session expired due to inactivity");
  }, []);

  // Show session timeout warning
  const showTimeoutWarning = useCallback(() => {
    setAuthState((prev) => ({ ...prev, sessionTimeoutWarning: true }));
  }, []);

  // Extend session (reset timers)
  const extendSession = useCallback(() => {
    clearTimers();
    updateActivity();
    setAuthState((prev) => ({
      ...prev,
      sessionTimeoutWarning: false,
      isSessionExpired: false,
    }));

    if (authState.user) {
      // Set warning timer (5 minutes before timeout)
      warningTimeoutRef.current = setTimeout(
        showTimeoutWarning,
        INACTIVITY_TIMEOUT - SESSION_TIMEOUT_WARNING
      );

      // Set timeout timer
      activityTimeoutRef.current = setTimeout(
        handleSessionTimeout,
        INACTIVITY_TIMEOUT
      );
    }
  }, [
    authState.user,
    clearTimers,
    updateActivity,
    showTimeoutWarning,
    handleSessionTimeout,
  ]);

  // Set up activity listeners
  useEffect(() => {
    const events = [
      "mousedown",
      "mousemove",
      "keypress",
      "scroll",
      "touchstart",
      "click",
    ];

    const handleActivity = () => {
      if (authState.user) {
        extendSession();
      }
    };

    // Add event listeners for user activity
    events.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    // Check for activity in other tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "lastActivity" && e.newValue) {
        const lastActivity = new Date(e.newValue);
        setAuthState((prev) => ({ ...prev, lastActivity }));
      }
    };

    window.addEventListener("storage", handleStorageChange);

    // Cleanup
    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });
      window.removeEventListener("storage", handleStorageChange);
      clearTimers();
    };
  }, [authState.user, extendSession, clearTimers]);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session?.user) {
        // Get user profile data
        const { data: userProfile } = await supabase
          .from("users")
          .select(
            `
            *,
            user_roles (
              role,
              is_active
            )
          `
          )
          .eq("id", session.user.id)
          .single();

        const enhancedUser: AuthUser = {
          ...session.user,
          tenant_id: userProfile?.tenant_id,
          role: userProfile?.user_roles?.[0]?.role,
          first_name: userProfile?.first_name,
          last_name: userProfile?.last_name,
        };

        setAuthState({
          user: enhancedUser,
          session,
          loading: false,
          isSessionExpired: false,
          lastActivity: new Date(),
          sessionTimeoutWarning: false,
        });

        // Initialize session management
        updateActivity();
        extendSession();
      } else {
        setAuthState({
          user: null,
          session: null,
          loading: false,
          isSessionExpired: false,
          lastActivity: null,
          sessionTimeoutWarning: false,
        });

        // Clear timers when no session
        clearTimers();
      }
    };

    getInitialSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        // Get user profile data
        const { data: userProfile } = await supabase
          .from("users")
          .select(
            `
              *,
              user_roles (
                role,
                is_active
              )
            `
          )
          .eq("id", session.user.id)
          .single();

        const enhancedUser: AuthUser = {
          ...session.user,
          tenant_id: userProfile?.tenant_id,
          role: userProfile?.user_roles?.[0]?.role,
          first_name: userProfile?.first_name,
          last_name: userProfile?.last_name,
        };

        setAuthState({
          user: enhancedUser,
          session,
          loading: false,
        });
      } else {
        setAuthState({
          user: null,
          session: null,
          loading: false,
        });
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/update-password`,
    });
    return { data, error };
  };

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
};
