import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';

interface Expense {
  id: string;
  tenant_id: string;
  company_id: string;
  supplier_id: string | null;
  expense_number: string | null;
  expense_date: string;
  category_id: string | null;
  description: string;
  amount: number;
  tax_amount: number;
  currency: string;
  receipt_url: string | null;
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'paid';
  created_at: string;
  updated_at: string;
  suppliers?: {
    name: string;
  };
  expense_categories?: {
    name: string;
  };
}

export const useExpenses = () => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchExpenses = async () => {
    if (!user?.tenant_id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('expenses')
        .select(`
          *,
          suppliers (
            name
          ),
          expense_categories (
            name
          )
        `)
        .eq('tenant_id', user.tenant_id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setExpenses(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const createExpense = async (expenseData: Partial<Expense>) => {
    if (!user?.tenant_id) throw new Error('No tenant ID');

    const { data, error } = await supabase
      .from('expenses')
      .insert({
        ...expenseData,
        tenant_id: user.tenant_id,
        created_by: user.id,
      })
      .select()
      .single();

    if (error) throw error;
    
    await fetchExpenses(); // Refresh the list
    return data;
  };

  const updateExpense = async (id: string, updates: Partial<Expense>) => {
    const { data, error } = await supabase
      .from('expenses')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    
    await fetchExpenses(); // Refresh the list
    return data;
  };

  const deleteExpense = async (id: string) => {
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id);

    if (error) throw error;
    
    await fetchExpenses(); // Refresh the list
  };

  useEffect(() => {
    fetchExpenses();
  }, [user?.tenant_id]);

  return {
    expenses,
    loading,
    error,
    createExpense,
    updateExpense,
    deleteExpense,
    refetch: fetchExpenses,
  };
};