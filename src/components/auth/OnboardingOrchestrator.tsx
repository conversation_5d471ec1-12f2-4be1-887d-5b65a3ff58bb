import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import CompanyStatusDetection from './CompanyStatusDetection';
import CompanyFormationGuide from './CompanyFormationGuide';
import AccountantPartnership from './AccountantPartnership';
import CompanyOnboarding from './CompanyOnboarding';
import { useLanguage } from '../../context/LanguageContext';

interface OnboardingOrchestratorProps {
  onComplete: (userData: any) => void;
}

type OnboardingStep = 
  | 'status-detection'
  | 'formation-guide'
  | 'accountant-partnership'
  | 'existing-company'
  | 'success';

interface OnboardingData {
  hasExistingCompany: boolean;
  wantsFormation?: boolean;
  selectedBusinessType?: string;
  leadData?: any;
  companyData?: any;
}

const OnboardingOrchestrator: React.FC<OnboardingOrchestratorProps> = ({ onComplete }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('status-detection');
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    hasExistingCompany: false
  });

  const handleStatusSelection = (hasCompany: boolean) => {
    setOnboardingData(prev => ({ ...prev, hasExistingCompany: hasCompany }));
    
    if (hasCompany) {
      setCurrentStep('existing-company');
    } else {
      setCurrentStep('formation-guide');
    }
  };

  const handleFormationGuideComplete = (wantsFormation: boolean, selectedType?: string) => {
    setOnboardingData(prev => ({ 
      ...prev, 
      wantsFormation, 
      selectedBusinessType: selectedType 
    }));

    if (wantsFormation) {
      setCurrentStep('accountant-partnership');
    } else {
      // User wants to handle formation themselves
      // We can still collect some basic info and proceed to success
      setCurrentStep('success');
    }
  };

  const handleAccountantPartnershipComplete = (leadData: any) => {
    setOnboardingData(prev => ({ ...prev, leadData }));
    setCurrentStep('success');
  };

  const handleAccountantPartnershipSkip = () => {
    setCurrentStep('success');
  };

  const handleExistingCompanyComplete = (companyData: any) => {
    setOnboardingData(prev => ({ ...prev, companyData }));
    setCurrentStep('success');
  };

  const handleBackToStatusDetection = () => {
    setCurrentStep('status-detection');
    setOnboardingData({ hasExistingCompany: false });
  };

  const handleBackToFormationGuide = () => {
    setCurrentStep('formation-guide');
  };

  const handleFinalComplete = () => {
    // Prepare final user data based on the onboarding path taken
    const finalUserData = {
      onboardingPath: onboardingData.hasExistingCompany ? 'existing-company' : 'new-company',
      hasExistingCompany: onboardingData.hasExistingCompany,
      ...onboardingData
    };

    onComplete(finalUserData);
  };

  const renderSuccessStep = () => (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-2xl w-full bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center"
      >
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring" }}
            className="text-4xl"
          >
            🎉
          </motion.div>
        </div>

        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Welcome to MoroccanERP!
        </h1>

        {onboardingData.hasExistingCompany ? (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              Your company information has been successfully configured. You're ready to start managing your business with our comprehensive ERP system.
            </p>
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                🚀 What's Next?
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1 text-left">
                <li>• Your tax compliance settings have been automatically configured</li>
                <li>• Start by adding your first customers and suppliers</li>
                <li>• Create your first invoice to test the system</li>
                <li>• Explore the dashboard for business insights</li>
              </ul>
            </div>
          </div>
        ) : onboardingData.wantsFormation ? (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              Thank you for your interest in our company formation services! Our partner accountant will contact you within 24 hours.
            </p>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 dark:text-green-400 mb-2">
                📞 What Happens Next?
              </h3>
              <ul className="text-sm text-green-800 dark:text-green-300 space-y-1 text-left">
                <li>• A certified accountant will call you within 24 hours</li>
                <li>• Free consultation to discuss your specific needs</li>
                <li>• Transparent pricing and timeline provided</li>
                <li>• Complete formation process handled professionally</li>
                <li>• Your ERP system will be pre-configured for your business</li>
              </ul>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                💼 Meanwhile, You Can:
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1 text-left">
                <li>• Explore the ERP system with demo data</li>
                <li>• Learn about Moroccan tax compliance features</li>
                <li>• Set up your business processes and workflows</li>
                <li>• Prepare your customer and supplier lists</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              You've chosen to handle company formation independently. We've provided you with comprehensive guidance to help you through the process.
            </p>
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <h3 className="font-semibold text-orange-900 dark:text-orange-400 mb-2">
                📋 Your Formation Checklist:
              </h3>
              <ul className="text-sm text-orange-800 dark:text-orange-300 space-y-1 text-left">
                <li>• Certificate of Negative (OMPIC)</li>
                <li>• Bank account opening and capital deposit</li>
                <li>• Notary registration and articles of association</li>
                <li>• Commercial register inscription</li>
                <li>• Tax registration and ICE number</li>
                <li>• CNSS registration (if applicable)</li>
              </ul>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
                🤝 Need Help Later?
              </h3>
              <p className="text-sm text-blue-800 dark:text-blue-300 text-left">
                Our partner accountants are always available if you change your mind. You can request assistance anytime from your settings page.
              </p>
            </div>
          </div>
        )}

        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleFinalComplete}
          className="mt-8 px-8 py-3 bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all"
        >
          Start Using MoroccanERP
        </motion.button>

        <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
          You can always update your company information later in settings
        </p>
      </motion.div>
    </div>
  );

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={currentStep}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        {currentStep === 'status-detection' && (
          <CompanyStatusDetection onStatusSelected={handleStatusSelection} />
        )}
        
        {currentStep === 'formation-guide' && (
          <CompanyFormationGuide 
            onComplete={handleFormationGuideComplete}
            onBack={handleBackToStatusDetection}
          />
        )}
        
        {currentStep === 'accountant-partnership' && onboardingData.selectedBusinessType && (
          <AccountantPartnership 
            businessType={onboardingData.selectedBusinessType}
            onComplete={handleAccountantPartnershipComplete}
            onSkip={handleAccountantPartnershipSkip}
          />
        )}
        
        {currentStep === 'existing-company' && (
          <CompanyOnboarding onComplete={handleExistingCompanyComplete} />
        )}
        
        {currentStep === 'success' && renderSuccessStep()}
      </motion.div>
    </AnimatePresence>
  );
};

export default OnboardingOrchestrator;
