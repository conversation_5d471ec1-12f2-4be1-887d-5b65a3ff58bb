import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Building, MapPin, FileText, CreditCard, CheckCircle } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface CompanyOnboardingProps {
  onComplete: (companyData: any) => void;
}

const CompanyOnboarding: React.FC<CompanyOnboardingProps> = ({ onComplete }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    companyName: '',
    legalForm: '',
    industry: '',
    iceNumber: '',
    vatNumber: '',
    address: '',
    city: '',
    postalCode: '',
    phone: '',
    email: '',
    website: '',
  });

  const steps = [
    { id: 1, name: 'Informations générales', icon: <Building size={20} /> },
    { id: 2, name: 'Informations légales', icon: <FileText size={20} /> },
    { id: 3, name: 'Adresse', icon: <MapPin size={20} /> },
    { id: 4, name: 'Finalisation', icon: <CheckCircle size={20} /> },
  ];

  const legalForms = [
    { value: 'auto-entrepreneur', label: 'Auto-entrepreneur (المقاول الذاتي)' },
    { value: 'sarl', label: 'SARL (شركة ذات المسؤولية المحدودة)' },
    { value: 'sa', label: 'SA (شركة مساهمة)' },
    { value: 'snc', label: 'SNC (شركة التضامن)' },
    { value: 'scs', label: 'SCS (شركة التوصية البسيطة)' },
  ];

  const industries = [
    { value: 'commerce', label: 'Commerce' },
    { value: 'services', label: 'Services' },
    { value: 'industrie', label: 'Industrie' },
    { value: 'agriculture', label: 'Agriculture' },
    { value: 'construction', label: 'Construction' },
    { value: 'transport', label: 'Transport' },
    { value: 'tourisme', label: 'Tourisme' },
    { value: 'technologie', label: 'Technologie' },
  ];

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(formData);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Informations générales de votre entreprise
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Commençons par les informations de base de votre entreprise.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Nom de l'entreprise *
          </label>
          <input
            type="text"
            required
            value={formData.companyName}
            onChange={(e) => updateFormData('companyName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="Ex: Entreprise Benali SARL"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Forme juridique *
          </label>
          <select
            required
            value={formData.legalForm}
            onChange={(e) => updateFormData('legalForm', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">Sélectionnez une forme juridique</option>
            {legalForms.map((form) => (
              <option key={form.value} value={form.value}>
                {form.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Secteur d'activité *
          </label>
          <select
            required
            value={formData.industry}
            onChange={(e) => updateFormData('industry', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">Sélectionnez un secteur</option>
            {industries.map((industry) => (
              <option key={industry.value} value={industry.value}>
                {industry.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Informations légales et fiscales
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Ces informations nous aideront à configurer automatiquement vos obligations fiscales.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Numéro ICE (Identifiant Commun de l'Entreprise)
          </label>
          <input
            type="text"
            value={formData.iceNumber}
            onChange={(e) => updateFormData('iceNumber', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="Ex: 001234567890123"
            maxLength={15}
          />
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            15 chiffres - Obligatoire pour toutes les entreprises au Maroc
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Numéro de TVA
          </label>
          <input
            type="text"
            value={formData.vatNumber}
            onChange={(e) => updateFormData('vatNumber', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="Ex: 12345678"
          />
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Requis si votre chiffre d'affaires dépasse 150,000 MAD/mois
          </p>
        </div>

        {formData.legalForm && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
              Obligations fiscales pour {legalForms.find(f => f.value === formData.legalForm)?.label}
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
              {formData.legalForm === 'auto-entrepreneur' ? (
                <>
                  <li>• Déclaration mensuelle simplifiée</li>
                  <li>• TVA optionnelle (si CA > 150k MAD/mois)</li>
                  <li>• Cotisations sociales volontaires</li>
                </>
              ) : (
                <>
                  <li>• Déclaration TVA mensuelle</li>
                  <li>• Impôt professionnel trimestriel</li>
                  <li>• Déclaration annuelle des revenus</li>
                  <li>• Cotisations sociales obligatoires</li>
                </>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Adresse et coordonnées
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Ces informations apparaîtront sur vos factures et documents officiels.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Adresse complète *
          </label>
          <input
            type="text"
            required
            value={formData.address}
            onChange={(e) => updateFormData('address', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="Ex: 123 Avenue Mohammed V"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Ville *
            </label>
            <input
              type="text"
              required
              value={formData.city}
              onChange={(e) => updateFormData('city', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Ex: Casablanca"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Code postal
            </label>
            <input
              type="text"
              value={formData.postalCode}
              onChange={(e) => updateFormData('postalCode', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Ex: 20000"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Téléphone *
            </label>
            <input
              type="tel"
              required
              value={formData.phone}
              onChange={(e) => updateFormData('phone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Ex: +212 5 22 12 34 56"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email professionnel *
            </label>
            <input
              type="email"
              required
              value={formData.email}
              onChange={(e) => updateFormData('email', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Ex: <EMAIL>"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Site web (optionnel)
          </label>
          <input
            type="url"
            value={formData.website}
            onChange={(e) => updateFormData('website', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="Ex: https://www.entreprise.ma"
          />
        </div>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="text-green-600 dark:text-green-400" size={32} />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Configuration terminée !
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Votre entreprise est maintenant configurée. Nous avons automatiquement défini vos obligations fiscales.
        </p>
      </div>

      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Résumé de votre configuration</h4>
        <div className="space-y-3 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Entreprise:</span>
            <span className="font-medium">{formData.companyName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Forme juridique:</span>
            <span className="font-medium">
              {legalForms.find(f => f.value === formData.legalForm)?.label}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Secteur:</span>
            <span className="font-medium">
              {industries.find(i => i.value === formData.industry)?.label}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Ville:</span>
            <span className="font-medium">{formData.city}</span>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 dark:text-blue-400 mb-2">
          Prochaines étapes
        </h4>
        <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
          <li>• Configurez vos premiers clients et fournisseurs</li>
          <li>• Créez votre première facture</li>
          <li>• Explorez les rapports fiscaux automatiques</li>
          <li>• Configurez vos notifications de rappel</li>
        </ul>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      case 4: return renderStep4();
      default: return renderStep1();
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.companyName && formData.legalForm && formData.industry;
      case 2:
        return true; // Optional fields
      case 3:
        return formData.address && formData.city && formData.phone && formData.email;
      case 4:
        return true;
      default:
        return false;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    currentStep >= step.id
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'border-gray-300 dark:border-gray-600 text-gray-400'
                  }`}>
                    {currentStep > step.id ? (
                      <CheckCircle size={16} />
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-2 ${
                      currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2">
              {steps.map((step) => (
                <div key={step.id} className="text-xs text-gray-600 dark:text-gray-400 max-w-20 text-center">
                  {step.name}
                </div>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {renderCurrentStep()}
          </motion.div>

          {/* Navigation Buttons */}
          <div className="mt-8 flex justify-between">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                currentStep === 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              Précédent
            </button>

            <button
              onClick={handleNext}
              disabled={!isStepValid()}
              className={`px-6 py-2 text-sm font-medium rounded-md ${
                isStepValid()
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {currentStep === steps.length ? 'Terminer' : 'Suivant'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyOnboarding;