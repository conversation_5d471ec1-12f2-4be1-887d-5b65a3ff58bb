import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import {
  User,
  Phone,
  Mail,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Star,
  Shield,
  Clock,
  Users,
} from "lucide-react";
import { useLanguage } from "../../context/LanguageContext";

interface AccountantPartnershipProps {
  businessType: string;
  onComplete: (leadData: any) => void;
  onSkip: () => void;
}

interface LeadData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  businessType: string;
  preferredTimeline: string;
  budget: string;
  additionalInfo: string;
}

const AccountantPartnership: React.FC<AccountantPartnershipProps> = ({
  businessType,
  onComplete,
  onSkip,
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [formData, setFormData] = useState<LeadData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    city: "",
    businessType,
    preferredTimeline: "",
    budget: "",
    additionalInfo: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const cities = [
    "Casablanca",
    "Rabat",
    "Marrakech",
    "Fès",
    "Tangier",
    "Agadir",
    "Meknes",
    "Oujda",
    "Kenitra",
    "Tetouan",
    "Safi",
    "Other",
  ];

  const timelines = [
    {
      value: "asap",
      label: "As soon as possible",
      description: "Within 1-2 weeks",
    },
    {
      value: "1month",
      label: "Within 1 month",
      description: "No rush, but soon",
    },
    {
      value: "3months",
      label: "Within 3 months",
      description: "Planning ahead",
    },
    {
      value: "flexible",
      label: "Flexible timeline",
      description: "Just exploring options",
    },
  ];

  const budgets = [
    {
      value: "3000-5000",
      label: "3,000 - 5,000 MAD",
      description: "Basic formation",
    },
    {
      value: "5000-8000",
      label: "5,000 - 8,000 MAD",
      description: "Standard service",
    },
    {
      value: "8000-12000",
      label: "8,000 - 12,000 MAD",
      description: "Premium package",
    },
    {
      value: "custom",
      label: "Custom quote",
      description: "Complex requirements",
    },
  ];

  const partnerBenefits = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: t("partnership.certified"),
      description: t("partnership.certified_desc"),
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: t("partnership.fast_processing"),
      description: t("partnership.fast_desc"),
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: t("partnership.guaranteed"),
      description: t("partnership.guaranteed_desc"),
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: t("partnership.ongoing_support"),
      description: t("partnership.ongoing_desc"),
    },
  ];

  const handleInputChange = (field: keyof LeadData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Here you would typically send the lead data to your backend
      // For now, we'll simulate the API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // In a real implementation, you'd send this to your CRM/lead management system
      console.log("Lead data:", formData);

      onComplete(formData);
    } catch (error) {
      console.error("Error submitting lead:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid =
    formData.firstName &&
    formData.lastName &&
    formData.email &&
    formData.phone &&
    formData.city &&
    formData.preferredTimeline;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-blue-600 px-8 py-6 text-white">
            <h1 className="text-2xl font-bold mb-2">
              🤝 {t("partnership.title")}
            </h1>
            <p className="text-green-100">
              {t("partnership.subtitle", { businessType })}
            </p>
          </div>

          <div className="p-8">
            {/* Benefits Section */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                {t("partnership.why_choose")}
              </h2>
              <div className="grid md:grid-cols-2 gap-4">
                {partnerBenefits.map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 mr-4">
                      {benefit.icon}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                        {benefit.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {benefit.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Lead Capture Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t("partnership.get_consultation")}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {t("partnership.consultation_desc")}
                </p>
              </div>

              {/* Personal Information */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("partnership.first_name")} *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      required
                      value={formData.firstName}
                      onChange={(e) =>
                        handleInputChange("firstName", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Your first name"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("partnership.last_name")} *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      required
                      value={formData.lastName}
                      onChange={(e) =>
                        handleInputChange("lastName", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Your last name"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("partnership.email")} *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("partnership.phone")} *
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="tel"
                      required
                      value={formData.phone}
                      onChange={(e) =>
                        handleInputChange("phone", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                      placeholder="+212 6XX XXX XXX"
                    />
                  </div>
                </div>
              </div>

              {/* City Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  City *
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <select
                    required
                    value={formData.city}
                    onChange={(e) => handleInputChange("city", e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select your city</option>
                    {cities.map((city) => (
                      <option key={city} value={city}>
                        {city}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Timeline Preference */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Preferred Timeline *
                </label>
                <div className="grid md:grid-cols-2 gap-3">
                  {timelines.map((timeline) => (
                    <motion.div
                      key={timeline.value}
                      whileHover={{ scale: 1.02 }}
                      className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                        formData.preferredTimeline === timeline.value
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-200 dark:border-gray-600 hover:border-green-300"
                      }`}
                      onClick={() =>
                        handleInputChange("preferredTimeline", timeline.value)
                      }
                    >
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 text-green-600 dark:text-green-400 mr-2" />
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white text-sm">
                            {timeline.label}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {timeline.description}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Budget Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Budget Range (Optional)
                </label>
                <div className="grid md:grid-cols-2 gap-3">
                  {budgets.map((budget) => (
                    <motion.div
                      key={budget.value}
                      whileHover={{ scale: 1.02 }}
                      className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                        formData.budget === budget.value
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-200 dark:border-gray-600 hover:border-green-300"
                      }`}
                      onClick={() => handleInputChange("budget", budget.value)}
                    >
                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 text-green-600 dark:text-green-400 mr-2" />
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white text-sm">
                            {budget.label}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {budget.description}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Additional Information */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Additional Information (Optional)
                </label>
                <textarea
                  value={formData.additionalInfo}
                  onChange={(e) =>
                    handleInputChange("additionalInfo", e.target.value)
                  }
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Tell us about your business plans, specific requirements, or any questions you have..."
                />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <button
                  type="submit"
                  disabled={!isFormValid || isSubmitting}
                  className={`flex-1 flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all ${
                    isFormValid && !isSubmitting
                      ? "bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl"
                      : "bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Connecting...
                    </>
                  ) : (
                    <>
                      Get Free Consultation
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </button>

                <button
                  type="button"
                  onClick={onSkip}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium"
                >
                  Skip for Now
                </button>
              </div>
            </form>

            {/* Trust Indicators */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center">
                  <Shield className="w-4 h-4 mr-1" />
                  Secure & Confidential
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  24h Response Time
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  No Obligation
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AccountantPartnership;
