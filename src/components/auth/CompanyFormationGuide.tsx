import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { motion, AnimatePresence } from "framer-motion";
import {
  Building,
  Users,
  Calculator,
  Calendar,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  DollarSign,
  FileText,
  Clock,
  TrendingUp,
} from "lucide-react";
import { useLanguage } from "../../context/LanguageContext";

interface CompanyFormationGuideProps {
  onComplete: (wantsFormation: boolean, selectedType?: string) => void;
  onBack: () => void;
}

interface BusinessType {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  minCapital: string;
  partners: string;
  liability: string;
  taxRate: string;
  complexity: "Simple" | "Medium" | "Complex";
  timeToForm: string;
  pros: string[];
  cons: string[];
  bestFor: string;
}

const CompanyFormationGuide: React.FC<CompanyFormationGuideProps> = ({
  onComplete,
  onBack,
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedBusinessType, setSelectedBusinessType] = useState<
    string | null
  >(null);
  const [wantsFormation, setWantsFormation] = useState<boolean | null>(null);

  const businessTypes: BusinessType[] = [
    {
      id: "auto-entrepreneur",
      name: "Auto-Entrepreneur",
      nameAr: "المقاول الذاتي",
      description: "Perfect for freelancers and small service providers",
      minCapital: "0 MAD",
      partners: "1 person only",
      liability: "Unlimited personal liability",
      taxRate: "1-3% of turnover",
      complexity: "Simple",
      timeToForm: "1-2 weeks",
      pros: [
        "Very simple registration process",
        "Low tax rates (1-3% of turnover)",
        "Minimal administrative burden",
        "No minimum capital required",
        "Quick setup",
      ],
      cons: [
        "Turnover limit: 500k MAD/year for services, 2M MAD for commerce",
        "Personal liability for business debts",
        "Limited business credibility",
        "Cannot deduct business expenses",
      ],
      bestFor: "Freelancers, consultants, small service providers",
    },
    {
      id: "sarl",
      name: "SARL (Limited Liability Company)",
      nameAr: "شركة ذات المسؤولية المحدودة",
      description: "Most popular choice for small to medium businesses",
      minCapital: "10,000 MAD",
      partners: "1-50 partners",
      liability: "Limited to capital contribution",
      taxRate: "31% corporate tax",
      complexity: "Medium",
      timeToForm: "4-6 weeks",
      pros: [
        "Limited liability protection",
        "Professional credibility",
        "Can deduct business expenses",
        "Flexible management structure",
        "Can have multiple partners",
      ],
      cons: [
        "Higher administrative requirements",
        "Annual financial statements required",
        "Higher tax rate than auto-entrepreneur",
        "Minimum capital requirement",
      ],
      bestFor: "Small to medium businesses, partnerships, growing companies",
    },
    {
      id: "sa",
      name: "SA (Public Limited Company)",
      nameAr: "شركة مساهمة",
      description: "For larger businesses planning to raise capital",
      minCapital: "300,000 MAD",
      partners: "Minimum 5 shareholders",
      liability: "Limited to share value",
      taxRate: "31% corporate tax",
      complexity: "Complex",
      timeToForm: "8-12 weeks",
      pros: [
        "Can raise capital from public",
        "High business credibility",
        "Limited liability protection",
        "Transferable shares",
        "Professional management structure",
      ],
      cons: [
        "High minimum capital requirement",
        "Complex administrative requirements",
        "Strict regulatory compliance",
        "Expensive to maintain",
        "Public disclosure requirements",
      ],
      bestFor:
        "Large businesses, companies seeking investment, public companies",
    },
  ];

  const steps = [
    { id: 1, title: "Business Types Overview", icon: <Building /> },
    { id: 2, title: "Tax Implications", icon: <Calculator /> },
    { id: 3, title: "Timeline & Process", icon: <Calendar /> },
    { id: 4, title: "Make Your Choice", icon: <CheckCircle /> },
  ];

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <div
            className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
              currentStep >= step.id
                ? "bg-blue-600 border-blue-600 text-white"
                : "border-gray-300 text-gray-400"
            }`}
          >
            {currentStep > step.id ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              step.icon
            )}
          </div>
          {index < steps.length - 1 && (
            <div
              className={`w-16 h-0.5 mx-2 ${
                currentStep > step.id ? "bg-blue-600" : "bg-gray-300"
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );

  const renderBusinessTypesOverview = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Choose Your Business Structure
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Understanding the different business types available in Morocco
        </p>
      </div>

      <div className="grid gap-6">
        {businessTypes.map((type) => (
          <motion.div
            key={type.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-6 border-2 rounded-xl cursor-pointer transition-all ${
              selectedBusinessType === type.id
                ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                : "border-gray-200 dark:border-gray-600 hover:border-blue-300"
            }`}
            onClick={() => setSelectedBusinessType(type.id)}
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {type.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {type.nameAr}
                </p>
              </div>
              <div
                className={`px-3 py-1 rounded-full text-xs font-medium ${
                  type.complexity === "Simple"
                    ? "bg-green-100 text-green-800"
                    : type.complexity === "Medium"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {type.complexity}
              </div>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {type.description}
            </p>

            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-green-600 mr-2" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Min Capital: {type.minCapital}
                  </span>
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-blue-600 mr-2" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Partners: {type.partners}
                  </span>
                </div>
                <div className="flex items-center">
                  <Calculator className="w-4 h-4 text-purple-600 mr-2" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Tax Rate: {type.taxRate}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center">
                  <Clock className="w-4 h-4 text-orange-600 mr-2" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Formation Time: {type.timeToForm}
                  </span>
                </div>
                <div className="flex items-center">
                  <TrendingUp className="w-4 h-4 text-indigo-600 mr-2" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Best for: {type.bestFor}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderTaxImplications = () => {
    const selectedType = businessTypes.find(
      (type) => type.id === selectedBusinessType
    );
    if (!selectedType) return null;

    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Tax Implications for {selectedType.name}
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Understanding your tax obligations and deadlines
          </p>
        </div>

        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            📊 Tax Breakdown for {selectedType.name}
          </h3>

          {selectedType.id === "auto-entrepreneur" ? (
            <div className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Monthly Declarations
                  </h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• 1% for commercial activities</li>
                    <li>• 2% for industrial activities</li>
                    <li>• 3% for service activities</li>
                  </ul>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Annual Limits
                  </h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Services: 500,000 MAD</li>
                    <li>• Commerce: 2,000,000 MAD</li>
                    <li>• Mixed: 2,000,000 MAD</li>
                  </ul>
                </div>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-green-800 dark:text-green-400 mb-2">
                  ✅ Benefits
                </h4>
                <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                  <li>• No VAT registration required (unless you choose to)</li>
                  <li>• No corporate income tax</li>
                  <li>• Simplified accounting</li>
                  <li>• Optional social security contributions</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Corporate Income Tax
                  </h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Standard rate: 31%</li>
                    <li>• Reduced rate: 17.5% (first 300k MAD)</li>
                    <li>• Annual declaration required</li>
                  </ul>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    VAT Registration
                  </h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <li>• Mandatory if turnover &gt; 180k MAD</li>
                    <li>• Monthly declarations</li>
                    <li>• Standard rate: 20%</li>
                  </ul>
                </div>
              </div>
              <div className="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-lg">
                <h4 className="font-medium text-orange-800 dark:text-orange-400 mb-2">
                  📅 Key Deadlines
                </h4>
                <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                  <li>• VAT: Monthly by 20th of following month</li>
                  <li>• Corporate tax: Annual by March 31st</li>
                  <li>• Professional tax: Quarterly</li>
                  <li>• Social security: Monthly</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderTimelineProcess = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Formation Timeline & Process
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Step-by-step process to register your business in Morocco
        </p>
      </div>

      <div className="space-y-4">
        {[
          {
            step: 1,
            title: "Certificate of Negative",
            time: "2-3 days",
            description: "Reserve your company name at OMPIC",
          },
          {
            step: 2,
            title: "Bank Account Opening",
            time: "1-2 days",
            description: "Open corporate bank account and deposit capital",
          },
          {
            step: 3,
            title: "Notary Registration",
            time: "1-2 days",
            description: "Sign articles of association with notary",
          },
          {
            step: 4,
            title: "Commercial Register",
            time: "3-5 days",
            description: "Register with Commercial Court",
          },
          {
            step: 5,
            title: "Tax Registration",
            time: "1-2 days",
            description: "Register for taxes and get ICE number",
          },
          {
            step: 6,
            title: "CNSS Registration",
            time: "1-2 days",
            description: "Register for social security (if employees)",
          },
        ].map((item, index) => (
          <motion.div
            key={item.step}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow"
          >
            <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
              {item.step}
            </div>
            <div className="ml-4 flex-1">
              <h4 className="font-medium text-gray-900 dark:text-white">
                {item.title}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {item.description}
              </p>
            </div>
            <div className="text-sm text-blue-600 dark:text-blue-400 font-medium">
              {item.time}
            </div>
          </motion.div>
        ))}
      </div>

      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg">
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3" />
          <div>
            <h4 className="font-medium text-yellow-800 dark:text-yellow-400 mb-1">
              💡 Pro Tip: Let Us Handle This For You
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              Our partner accountants can handle the entire process for you,
              ensuring compliance and saving you time. Typical cost: 3,000-8,000
              MAD depending on business type.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFinalChoice = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Ready to Start Your Business?
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Choose how you'd like to proceed with your company formation
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 border-2 rounded-xl cursor-pointer transition-all ${
            wantsFormation === true
              ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
              : "border-gray-200 dark:border-gray-600 hover:border-blue-300"
          }`}
          onClick={() => setWantsFormation(true)}
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Yes, Help Me Form My Company
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              Connect with our certified partner accountants for professional
              company formation
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-center text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4 mr-2" />
                Expert guidance throughout
              </div>
              <div className="flex items-center justify-center text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4 mr-2" />
                All paperwork handled
              </div>
              <div className="flex items-center justify-center text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4 mr-2" />
                Compliance guaranteed
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`p-6 border-2 rounded-xl cursor-pointer transition-all ${
            wantsFormation === false
              ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
              : "border-gray-200 dark:border-gray-600 hover:border-blue-300"
          }`}
          onClick={() => setWantsFormation(false)}
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              I'll Handle It Myself
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              Proceed with the information provided and handle formation
              independently
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-center text-blue-600 dark:text-blue-400">
                <CheckCircle className="w-4 h-4 mr-2" />
                Complete guide provided
              </div>
              <div className="flex items-center justify-center text-blue-600 dark:text-blue-400">
                <CheckCircle className="w-4 h-4 mr-2" />
                Start using ERP immediately
              </div>
              <div className="flex items-center justify-center text-blue-600 dark:text-blue-400">
                <CheckCircle className="w-4 h-4 mr-2" />
                Support available anytime
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {wantsFormation === true && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-6 rounded-lg"
        >
          <h4 className="font-semibold text-green-800 dark:text-green-400 mb-3">
            🎉 Excellent Choice! Here's What Happens Next:
          </h4>
          <ol className="text-sm text-green-700 dark:text-green-300 space-y-2">
            <li>
              1. We'll connect you with a certified accountant within 24 hours
            </li>
            <li>2. Free consultation to discuss your specific needs</li>
            <li>3. Transparent pricing and timeline provided</li>
            <li>4. Complete formation process handled professionally</li>
            <li>
              5. Your ERP system will be pre-configured for your business type
            </li>
          </ol>
        </motion.div>
      )}
    </div>
  );

  const renderNavigation = () => (
    <div className="flex justify-between mt-8">
      <button
        onClick={
          currentStep === 1 ? onBack : () => setCurrentStep(currentStep - 1)
        }
        className="flex items-center px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back
      </button>

      <button
        onClick={() => {
          if (currentStep < 4) {
            setCurrentStep(currentStep + 1);
          } else {
            onComplete(
              wantsFormation || false,
              selectedBusinessType || undefined
            );
          }
        }}
        disabled={currentStep === 1 && !selectedBusinessType}
        className={`flex items-center px-6 py-2 rounded-lg font-medium ${
          (currentStep === 1 && !selectedBusinessType) ||
          (currentStep === 4 && wantsFormation === null)
            ? "bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed"
            : "bg-blue-600 hover:bg-blue-700 text-white"
        }`}
      >
        {currentStep === 4 ? "Complete" : "Next"}
        <ArrowRight className="w-4 h-4 ml-2" />
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8"
        >
          {renderStepIndicator()}

          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStep === 1 && renderBusinessTypesOverview()}
              {currentStep === 2 && renderTaxImplications()}
              {currentStep === 3 && renderTimelineProcess()}
              {currentStep === 4 && renderFinalChoice()}
            </motion.div>
          </AnimatePresence>

          {renderNavigation()}
        </motion.div>
      </div>
    </div>
  );
};

export default CompanyFormationGuide;
