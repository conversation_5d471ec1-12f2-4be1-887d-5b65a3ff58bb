import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Building, Users, BookOpen, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface CompanyStatusDetectionProps {
  onStatusSelected: (hasCompany: boolean) => void;
}

const CompanyStatusDetection: React.FC<CompanyStatusDetectionProps> = ({ onStatusSelected }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [selectedOption, setSelectedOption] = useState<boolean | null>(null);

  const handleContinue = () => {
    if (selectedOption !== null) {
      onStatusSelected(selectedOption);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl w-full bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            🇲🇦 Welcome to MoroccanERP
          </h1>
          <p className="text-blue-100">
            Let's get you started with the perfect setup for your business
          </p>
        </div>

        {/* Content */}
        <div className="p-8">
          <div className="text-center mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              What's your current business situation?
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Choose the option that best describes your situation to get personalized guidance
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            {/* Existing Company Option */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all ${
                selectedOption === true
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-blue-300'
              }`}
              onClick={() => setSelectedOption(true)}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <Building className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    I already have a company
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    My business is already registered in Morocco with ICE number, RC, and other legal documents
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-green-600 dark:text-green-400">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Quick setup (5 minutes)
                    </div>
                    <div className="flex items-center text-sm text-green-600 dark:text-green-400">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Immediate access to all features
                    </div>
                    <div className="flex items-center text-sm text-green-600 dark:text-green-400">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Automatic tax compliance setup
                    </div>
                  </div>
                </div>
              </div>
              {selectedOption === true && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
            </motion.div>

            {/* No Company Option */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all ${
                selectedOption === false
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-blue-300'
              }`}
              onClick={() => setSelectedOption(false)}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                    <BookOpen className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    I need to create a company
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                    I want to start a business in Morocco and need guidance on company formation and legal requirements
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
                      <BookOpen className="w-4 h-4 mr-2" />
                      Complete business formation guide
                    </div>
                    <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
                      <Users className="w-4 h-4 mr-2" />
                      Expert accountant partnership
                    </div>
                    <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      Tax implications explained
                    </div>
                  </div>
                </div>
              </div>
              {selectedOption === false && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
            </motion.div>
          </div>

          {/* Continue Button */}
          <div className="text-center">
            <button
              onClick={handleContinue}
              disabled={selectedOption === null}
              className={`inline-flex items-center px-8 py-3 rounded-lg font-medium transition-all ${
                selectedOption !== null
                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              Continue
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>
          </div>

          {/* Help Text */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Don't worry, you can always change this later in your settings
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default CompanyStatusDetection;
