import React, { useState, useEffect } from "react";
import { Eye, EyeOff, AlertCircle, CheckCircle } from "lucide-react";
import { ValidationRule, validateField } from "../../utils/validation";

interface FormInputProps {
  label: string;
  name: string;
  type?:
    | "text"
    | "email"
    | "password"
    | "tel"
    | "number"
    | "url"
    | "date"
    | "textarea"
    | "select";
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  validation?: ValidationRule;
  error?: string;
  success?: boolean;
  helpText?: string;
  options?: { value: string; label: string }[]; // For select inputs
  rows?: number; // For textarea
  icon?: React.ReactNode;
  className?: string;
  autoComplete?: string;
  maxLength?: number;
}

const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  type = "text",
  value,
  onChange,
  onBlur,
  placeholder,
  disabled = false,
  required = false,
  validation,
  error,
  success = false,
  helpText,
  options = [],
  rows = 3,
  icon,
  className = "",
  autoComplete,
  maxLength,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [internalError, setInternalError] = useState<string | null>(null);
  const [touched, setTouched] = useState(false);

  // Validate on value change if validation rules are provided
  useEffect(() => {
    if (validation && touched) {
      const validationError = validateField(value, validation, label);
      setInternalError(validationError);
    }
  }, [value, validation, label, touched]);

  const displayError = error || internalError;
  const hasError = !!displayError;
  const showSuccess = success && !hasError && touched && value;

  const handleBlur = () => {
    setTouched(true);
    if (validation) {
      const validationError = validateField(value, validation, label);
      setInternalError(validationError);
    }
    onBlur?.();
  };

  const handleChange = (newValue: string) => {
    onChange(newValue);
    // Clear error when user starts typing
    if (hasError && newValue !== value) {
      setInternalError(null);
    }
  };

  const baseInputClasses = `
    block w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-0
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-white dark:disabled:bg-gray-600
    ${
      hasError
        ? "border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600"
        : showSuccess
        ? "border-green-300 focus:border-green-500 focus:ring-green-500 dark:border-green-600"
        : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600"
    }
    ${icon ? "pl-10" : ""}
    ${type === "password" ? "pr-10" : ""}
  `
    .trim()
    .replace(/\s+/g, " ");

  const renderInput = () => {
    switch (type) {
      case "textarea":
        return (
          <textarea
            id={name}
            name={name}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            rows={rows}
            maxLength={maxLength}
            className={`${baseInputClasses} resize-vertical`}
          />
        );

      case "select":
        return (
          <select
            id={name}
            name={name}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={handleBlur}
            disabled={disabled}
            required={required}
            className={baseInputClasses}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case "password":
        return (
          <div className="relative">
            <input
              id={name}
              name={name}
              type={showPassword ? "text" : "password"}
              value={value}
              onChange={(e) => handleChange(e.target.value)}
              onBlur={handleBlur}
              placeholder={placeholder}
              disabled={disabled}
              required={required}
              autoComplete={autoComplete}
              maxLength={maxLength}
              className={baseInputClasses}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
              disabled={disabled}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
        );

      default:
        return (
          <input
            id={name}
            name={name}
            type={type}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            autoComplete={autoComplete}
            maxLength={maxLength}
            className={baseInputClasses}
          />
        );
    }
  };

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Label */}
      <label
        htmlFor={name}
        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
      >
        {icon && <span className="inline-flex items-center mr-2">{icon}</span>}
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {/* Input Container */}
      <div className="relative">
        {icon && type !== "password" && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-400">{icon}</span>
          </div>
        )}

        {renderInput()}

        {/* Success Icon */}
        {showSuccess && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <CheckCircle className="h-4 w-4 text-green-500" />
          </div>
        )}
      </div>

      {/* Error Message */}
      {hasError && (
        <div className="flex items-center mt-1">
          <AlertCircle className="h-4 w-4 text-red-500 mr-1" />
          <p className="text-sm text-red-600 dark:text-red-400">
            {displayError}
          </p>
        </div>
      )}

      {/* Help Text */}
      {helpText && !hasError && (
        <p className="text-sm text-gray-500 dark:text-gray-400">{helpText}</p>
      )}

      {/* Character Count */}
      {maxLength && (type === "text" || type === "textarea") && (
        <p className="text-xs text-gray-400 text-right">
          {value.length}/{maxLength}
        </p>
      )}
    </div>
  );
};

export default FormInput;

// Example usage:
/*
import FormInput from './components/common/FormInput';
import { VALIDATION_PATTERNS } from './utils/validation';

const MyForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  return (
    <form>
      <FormInput
        label="Email Address"
        name="email"
        type="email"
        value={email}
        onChange={setEmail}
        required
        validation={{
          required: true,
          pattern: VALIDATION_PATTERNS.email
        }}
        placeholder="<EMAIL>"
        icon={<Mail className="h-4 w-4" />}
      />

      <FormInput
        label="Password"
        name="password"
        type="password"
        value={password}
        onChange={setPassword}
        required
        validation={{
          required: true,
          minLength: 8,
          pattern: VALIDATION_PATTERNS.password
        }}
        helpText="Must contain at least 8 characters with uppercase, lowercase, and numbers"
      />
    </form>
  );
};
*/
