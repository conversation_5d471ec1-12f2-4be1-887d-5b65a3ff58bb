import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

const LanguageSwitcher: React.FC = () => {
  const { t } = useTranslation();
  const { language, setLanguage } = useLanguage();
  const [isOpen, setIsOpen] = React.useState(false);
  
  const toggleDropdown = () => setIsOpen(!isOpen);
  
  const selectLanguage = (lang: 'en' | 'fr' | 'ar') => {
    setLanguage(lang);
    setIsOpen(false);
  };
  
  return (
    <div className="relative">
      <button 
        onClick={toggleDropdown}
        className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Globe size={18} />
        <span className="hidden sm:inline">{language.toUpperCase()}</span>
      </button>
      
      {isOpen && (
        <div 
          className="absolute mt-2 w-48 bg-white dark:bg-gray-900 rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-10"
          style={{ right: language === 'ar' ? 'auto' : 0, left: language === 'ar' ? 0 : 'auto' }}
        >
          <button
            onClick={() => selectLanguage('fr')}
            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'fr' ? 'font-semibold bg-gray-50 dark:bg-gray-800' : ''}`}
          >
            {t('language.french')}
          </button>
          <button
            onClick={() => selectLanguage('ar')}
            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ar' ? 'font-semibold bg-gray-50 dark:bg-gray-800' : ''}`}
          >
            {t('language.arabic')}
          </button>
          <button
            onClick={() => selectLanguage('en')}
            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'en' ? 'font-semibold bg-gray-50 dark:bg-gray-800' : ''}`}
          >
            {t('language.english')}
          </button>
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;