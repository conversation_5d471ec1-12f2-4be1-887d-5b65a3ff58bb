import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Clock, RefreshCw, LogOut } from "lucide-react";
import { useAuth } from "../../hooks/useAuth";

const SessionTimeoutWarning: React.FC = () => {
  const { sessionTimeoutWarning, signOut } = useAuth();
  const [countdown, setCountdown] = useState(300); // 5 minutes in seconds

  useEffect(() => {
    if (sessionTimeoutWarning) {
      setCountdown(300); // Reset countdown when warning appears

      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            // Auto logout when countdown reaches 0
            signOut();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [sessionTimeoutWarning, signOut]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleExtendSession = () => {
    // For now, just close the warning - session management will be re-implemented later
    // extendSession();
  };

  const handleLogout = () => {
    signOut();
  };

  return (
    <AnimatePresence>
      {sessionTimeoutWarning && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          >
            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6"
            >
              {/* Icon */}
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-yellow-100 dark:bg-yellow-900/20 rounded-full mb-4">
                <Clock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>

              {/* Title */}
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center mb-2">
                Session Timeout Warning
              </h3>

              {/* Message */}
              <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
                Your session will expire in{" "}
                <strong>{formatTime(countdown)}</strong> due to inactivity.
                Would you like to extend your session?
              </p>

              {/* Countdown Progress */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6">
                <motion.div
                  className="bg-yellow-500 h-2 rounded-full"
                  initial={{ width: "100%" }}
                  animate={{ width: `${(countdown / 300) * 100}%` }}
                  transition={{ duration: 1, ease: "linear" }}
                />
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={handleExtendSession}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Extend Session
                </button>

                <button
                  onClick={handleLogout}
                  className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout Now
                </button>
              </div>

              {/* Auto logout notice */}
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
                You will be automatically logged out when the timer reaches
                zero.
              </p>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default SessionTimeoutWarning;
