import React from 'react';
import { motion } from 'framer-motion';
import { Clock, RefreshCw, Home } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

const SessionExpired: React.FC = () => {
  const navigate = useNavigate();
  const { isSessionExpired } = useAuth();

  const handleLoginAgain = () => {
    navigate('/auth/login');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  if (!isSessionExpired) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-gray-50 dark:bg-gray-900 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 text-center"
      >
        {/* Icon */}
        <div className="flex items-center justify-center w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
          <Clock className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>

        {/* Title */}
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Session Expired
        </h2>

        {/* Message */}
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Your session has expired due to inactivity. For your security, you have been automatically logged out.
        </p>

        {/* Security Notice */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3 mb-6">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Security Notice:</strong> This helps protect your account from unauthorized access.
          </p>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <button
            onClick={handleLoginAgain}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Login Again
          </button>
          
          <button
            onClick={handleGoHome}
            className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Home className="w-4 h-4 mr-2" />
            Go to Homepage
          </button>
        </div>

        {/* Tips */}
        <div className="mt-6 text-left">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Tips to avoid session timeout:
          </h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li>• Stay active by moving your mouse or typing</li>
            <li>• Save your work frequently</li>
            <li>• Extend your session when prompted</li>
          </ul>
        </div>
      </motion.div>
    </div>
  );
};

export default SessionExpired;
