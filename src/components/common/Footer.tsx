import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../context/LanguageContext';

const Footer: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const year = new Date().getFullYear();
  
  return (
    <footer className="bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">{t('app.name')}</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{t('app.tagline')}</p>
          </div>
          
          <div className={isRTL ? 'mr-auto' : 'ml-auto'} style={{ textAlign: isRTL ? 'right' : 'left' }}>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">{t('landing.features.title')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('landing.features.financial.title')}</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('landing.features.tax.title')}</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('landing.features.ai.title')}</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('landing.features.multilingual.title')}</a></li>
            </ul>
          </div>
          
          <div className={isRTL ? 'mr-auto' : 'ml-auto'} style={{ textAlign: isRTL ? 'right' : 'left' }}>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">{t('landing.pricing.title')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('landing.pricing.free.title')}</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('landing.pricing.premium.title')}</a></li>
            </ul>
          </div>
          
          <div className={isRTL ? 'mr-auto' : 'ml-auto'} style={{ textAlign: isRTL ? 'right' : 'left' }}>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">{t('nav.settings')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('auth.login')}</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-blue-800 dark:hover:text-blue-400">{t('auth.signup')}</a></li>
            </ul>
          </div>
        </div>
        
        <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-800">
          <p className="text-gray-500 dark:text-gray-400 text-center">
            &copy; {year} {t('app.name')}. {t('footer.rights')}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;