import React from 'react';
import { useTranslation } from 'react-i18next';
import { AlertCircle, ArrowUpRight } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import { Invoice } from '../../types';

interface InvoicesListProps {
  invoices: Invoice[];
  title: string;
}

const InvoicesList: React.FC<InvoicesListProps> = ({ invoices, title }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
        <a href="#" className="text-sm text-blue-600 dark:text-blue-400 flex items-center">
          <span>{t('view.all')}</span>
          <ArrowUpRight size={16} className={isRTL ? 'mr-1' : 'ml-1'} />
        </a>
      </div>
      
      {invoices.length === 0 ? (
        <div className="flex items-center justify-center p-6 text-gray-500 dark:text-gray-400">
          <AlertCircle size={18} className="mr-2" />
          <span>{t('no.invoices')}</span>
        </div>
      ) : (
        <div className="space-y-3">
          {invoices.map((invoice) => (
            <div 
              key={invoice.id} 
              className="flex justify-between items-center p-3 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{invoice.clientName}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{invoice.number}</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900 dark:text-white">{invoice.total} MAD</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{invoice.dueDate}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default InvoicesList;