import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

const UpgradeCard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  return (
    <motion.div 
      className="bg-gradient-to-r from-blue-700 to-blue-900 rounded-xl shadow-md overflow-hidden"
      whileHover={{ scale: 1.02 }}
      transition={{ type: 'spring', stiffness: 300 }}
    >
      <div className="p-6 text-white">
        <div className={`flex ${isRTL ? 'flex-row-reverse' : 'flex-row'} items-center mb-4`}>
          <div className="p-2 rounded-full bg-blue-500/30 mr-4">
            <Sparkles size={24} />
          </div>
          <h3 className="text-lg font-semibold">{t('dashboard.upgrade.title')}</h3>
        </div>
        
        <p className="mb-6 text-blue-100">{t('dashboard.upgrade.desc')}</p>
        
        <button className="w-full bg-white text-blue-800 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors">
          {t('dashboard.upgrade.cta')}
        </button>
      </div>
    </motion.div>
  );
};

export default UpgradeCard;