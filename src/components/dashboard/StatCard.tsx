import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '../../context/LanguageContext';

interface StatCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: React.ReactNode;
  colorClass?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  colorClass = 'bg-blue-500 dark:bg-blue-600'
}) => {
  const { isRTL } = useLanguage();
  
  return (
    <motion.div 
      className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden"
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
    >
      <div className="p-6">
        <div className={`flex ${isRTL ? 'flex-row-reverse' : 'flex-row'} items-center mb-4`}>
          <div className={`p-3 rounded-full ${colorClass} text-white mr-4`}>
            {icon}
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          </div>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-300">{subtitle}</p>
      </div>
    </motion.div>
  );
};

export default StatCard;