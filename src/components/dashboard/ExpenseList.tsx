import React from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowUpRight } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import { Expense } from '../../types';

interface ExpenseListProps {
  expenses: Expense[];
}

const ExpenseList: React.FC<ExpenseListProps> = ({ expenses }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('dashboard.expenses.recent')}
        </h3>
        <a href="#" className="text-sm text-blue-600 dark:text-blue-400 flex items-center">
          <span>{t('view.all')}</span>
          <ArrowUpRight size={16} className={isRTL ? 'mr-1' : 'ml-1'} />
        </a>
      </div>
      
      <div className="space-y-3">
        {expenses.map((expense) => (
          <div 
            key={expense.id} 
            className="flex justify-between items-center p-3 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div>
              <p className="font-medium text-gray-900 dark:text-white">{expense.vendor}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">{expense.category}</p>
            </div>
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">{expense.amount} MAD</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">{expense.date}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExpenseList;