import React from 'react';
import { useTranslation } from 'react-i18next';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { useLanguage } from '../../context/LanguageContext';
import { CashFlowData } from '../../types';

interface CashFlowChartProps {
  data: CashFlowData[];
}

const CashFlowChart: React.FC<CashFlowChartProps> = ({ data }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 h-80">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{t('dashboard.cash.flow')}</h3>
      
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{ top: 5, right: 20, left: 20, bottom: 5 }}
          >
            <defs>
              <linearGradient id="colorIncome" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#2D8B7D" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#2D8B7D" stopOpacity={0.1} />
              </linearGradient>
              <linearGradient id="colorExpenses" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#D4AF37" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#D4AF37" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              reversed={isRTL} 
              tick={{ fill: '#6B7280' }}
            />
            <YAxis tick={{ fill: '#6B7280' }} />
            <Tooltip />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="income" 
              name={isRTL ? 'الدخل' : 'Income'} 
              stroke="#2D8B7D" 
              fillOpacity={1} 
              fill="url(#colorIncome)" 
            />
            <Area 
              type="monotone" 
              dataKey="expenses" 
              name={isRTL ? 'النفقات' : 'Expenses'} 
              stroke="#D4AF37" 
              fillOpacity={1} 
              fill="url(#colorExpenses)" 
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CashFlowChart;