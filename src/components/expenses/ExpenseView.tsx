import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Edit, Download, X, Calendar, User, Receipt, DollarSign } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import { ExpenseData } from '../../pages/Expenses';

interface ExpenseViewProps {
  expense: ExpenseData;
  onEdit: () => void;
  onClose: () => void;
}

const ExpenseView: React.FC<ExpenseViewProps> = ({
  expense,
  onEdit,
  onClose
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number, currency: string = 'MAD') => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency === 'MAD' ? 'EUR' : currency,
      minimumFractionDigits: 2
    }).format(amount).replace('€', 'MAD');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'paid': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {t('expenses.view.title')}
            </h2>
            <div className="flex items-center mt-2 space-x-4">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(expense.status)}`}>
                {t(`expenses.status.${expense.status}`)}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(expense.expense_date)}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={onEdit}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <Edit size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              {t('common.edit')}
            </button>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>
      </div>

      {/* Expense Content */}
      <div className="p-8">
        {/* Expense Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('expenses.view.details')}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('expenses.form.description')}
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {expense.description}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('expenses.form.category')}
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {expense.expense_categories?.name || '-'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('expenses.form.supplier')}
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {expense.suppliers?.name || t('expenses.no_supplier')}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('expenses.view.financial')}
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t('expenses.form.amount')}:
                </span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(expense.amount, expense.currency)}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {t('expenses.form.tax_amount')}:
                </span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(expense.tax_amount, expense.currency)}
                </span>
              </div>
              
              <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-600">
                <span className="text-base font-semibold text-gray-900 dark:text-white">
                  {t('expenses.form.total_amount')}:
                </span>
                <span className="text-lg font-bold text-gray-900 dark:text-white">
                  {formatCurrency(expense.amount + expense.tax_amount, expense.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Receipt */}
        {expense.receipt_url && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('expenses.view.receipt')}
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Receipt size={20} className="text-green-500 mr-3" />
                  <span className="text-sm text-gray-900 dark:text-white">
                    {t('expenses.view.receipt_attached')}
                  </span>
                </div>
                <button
                  onClick={() => window.open(expense.receipt_url!, '_blank')}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 transition-colors"
                >
                  <Download size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('expenses.view.download')}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>{t('expenses.view.footer_text')}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ExpenseView;