import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Upload, Camera, FileText, X, Loader, CheckCircle, AlertCircle } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface ReceiptUploadProps {
  onReceiptProcessed: (extractedData: any) => void;
  onCancel: () => void;
}

const ReceiptUpload: React.FC<ReceiptUploadProps> = ({
  onReceiptProcessed,
  onCancel
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const [dragActive, setDragActive] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [extractedData, setExtractedData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    setUploadedFile(file);
    setProcessing(true);
    setError(null);

    try {
      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock extracted data - in real implementation, this would come from AI service
      const mockExtractedData = {
        description: 'Restaurant Le Petit Casablanca',
        amount: 245.50,
        tax_amount: 49.10,
        expense_date: new Date().toISOString().split('T')[0],
        currency: 'MAD',
        category_id: 'meals',
        receipt_url: URL.createObjectURL(file)
      };
      
      setExtractedData(mockExtractedData);
    } catch (err) {
      setError('Erreur lors du traitement du reçu. Veuillez réessayer.');
    } finally {
      setProcessing(false);
    }
  };

  const handleConfirm = () => {
    if (extractedData) {
      onReceiptProcessed(extractedData);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 2
    }).format(amount).replace('€', 'MAD');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm"
    >
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('expenses.upload.title')}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X size={24} />
          </button>
        </div>
      </div>

      <div className="p-6">
        {!uploadedFile && (
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-300 dark:border-gray-600'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="space-y-4">
              <div className="flex justify-center">
                <Upload size={48} className="text-gray-400" />
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t('expenses.upload.drag_drop')}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  {t('expenses.upload.or_click')}
                </p>
              </div>
              
              <div className="flex justify-center space-x-4">
                <label className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                  <Upload size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('expenses.upload.choose_file')}
                  <input
                    type="file"
                    className="sr-only"
                    accept="image/*,.pdf"
                    onChange={handleFileInput}
                  />
                </label>
                
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                  <Camera size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('expenses.upload.take_photo')}
                </button>
              </div>
              
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {t('expenses.upload.supported_formats')}
              </p>
            </div>
          </div>
        )}

        {uploadedFile && processing && (
          <div className="text-center py-8">
            <Loader size={48} className="mx-auto text-blue-600 animate-spin mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t('expenses.upload.processing')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('expenses.upload.ai_extracting')}
            </p>
          </div>
        )}

        {error && (
          <div className="text-center py-8">
            <AlertCircle size={48} className="mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {t('expenses.upload.error')}
            </h3>
            <p className="text-sm text-red-600 dark:text-red-400 mb-4">
              {error}
            </p>
            <button
              onClick={() => {
                setUploadedFile(null);
                setError(null);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              {t('expenses.upload.try_again')}
            </button>
          </div>
        )}

        {extractedData && !processing && (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle size={48} className="mx-auto text-green-500 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('expenses.upload.success')}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('expenses.upload.data_extracted')}
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                {t('expenses.upload.extracted_data')}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 dark:text-gray-400">{t('expenses.form.description')}:</span>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {extractedData.description}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">{t('expenses.form.amount')}:</span>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatCurrency(extractedData.amount)}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">{t('expenses.form.tax_amount')}:</span>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatCurrency(extractedData.tax_amount)}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">{t('expenses.form.date')}:</span>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {new Date(extractedData.expense_date).toLocaleDateString('fr-FR')}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setUploadedFile(null);
                  setExtractedData(null);
                }}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              >
                {t('expenses.upload.upload_another')}
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors"
              >
                {t('expenses.upload.create_expense')}
              </button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ReceiptUpload;