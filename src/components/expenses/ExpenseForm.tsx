import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Save, X, Upload, Receipt } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import { ExpenseData } from '../../pages/Expenses';

interface ExpenseFormProps {
  expense?: ExpenseData | null;
  onSave: (expenseData: Partial<ExpenseData>) => void;
  onCancel: () => void;
  isEdit: boolean;
}

const ExpenseForm: React.FC<ExpenseFormProps> = ({
  expense,
  onSave,
  onCancel,
  isEdit
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    expense_date: new Date().toISOString().split('T')[0],
    description: '',
    amount: 0,
    tax_amount: 0,
    currency: 'MAD',
    category_id: '',
    supplier_id: '',
    receipt_url: '',
    status: 'draft' as const
  });

  const [receiptFile, setReceiptFile] = useState<File | null>(null);

  useEffect(() => {
    if (expense && isEdit) {
      setFormData({
        expense_date: expense.expense_date,
        description: expense.description,
        amount: expense.amount,
        tax_amount: expense.tax_amount,
        currency: expense.currency,
        category_id: expense.category_id || '',
        supplier_id: expense.suppliers?.name || '',
        receipt_url: expense.receipt_url || '',
        status: expense.status
      });
    }
  }, [expense, isEdit]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setReceiptFile(file);
      // In a real implementation, you would upload the file to storage
      // and get back a URL to store in receipt_url
      setFormData({ ...formData, receipt_url: URL.createObjectURL(file) });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 2
    }).format(amount).replace('€', 'MAD');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm"
    >
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEdit ? t('expenses.edit') : t('expenses.create')}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X size={24} />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('expenses.form.date')} *
            </label>
            <input
              type="date"
              required
              value={formData.expense_date}
              onChange={(e) => setFormData({ ...formData, expense_date: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('expenses.form.amount')} *
            </label>
            <input
              type="number"
              required
              min="0"
              step="0.01"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="0.00"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('expenses.form.tax_amount')}
            </label>
            <input
              type="number"
              min="0"
              step="0.01"
              value={formData.tax_amount}
              onChange={(e) => setFormData({ ...formData, tax_amount: parseFloat(e.target.value) || 0 })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="0.00"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('expenses.form.currency')}
            </label>
            <select
              value={formData.currency}
              onChange={(e) => setFormData({ ...formData, currency: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="MAD">MAD - Dirham Marocain</option>
              <option value="EUR">EUR - Euro</option>
              <option value="USD">USD - Dollar US</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('expenses.form.category')}
            </label>
            <select
              value={formData.category_id}
              onChange={(e) => setFormData({ ...formData, category_id: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="">{t('expenses.form.select_category')}</option>
              <option value="office">Fournitures de bureau</option>
              <option value="telecom">Télécommunications</option>
              <option value="rent">Loyer</option>
              <option value="utilities">Services publics</option>
              <option value="travel">Déplacements</option>
              <option value="meals">Repas d'affaires</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('expenses.form.status')}
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              <option value="draft">{t('expenses.status.draft')}</option>
              <option value="pending">{t('expenses.status.pending')}</option>
              <option value="approved">{t('expenses.status.approved')}</option>
              <option value="rejected">{t('expenses.status.rejected')}</option>
              <option value="paid">{t('expenses.status.paid')}</option>
            </select>
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('expenses.form.description')} *
          </label>
          <textarea
            required
            rows={3}
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder={t('expenses.form.description_placeholder')}
          />
        </div>

        {/* Receipt Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('expenses.form.receipt')}
          </label>
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
            <div className="text-center">
              <Receipt size={48} className="mx-auto text-gray-400 mb-4" />
              <div className="flex text-sm text-gray-600 dark:text-gray-400">
                <label htmlFor="receipt-upload" className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                  <span>{t('expenses.form.upload_receipt')}</span>
                  <input
                    id="receipt-upload"
                    name="receipt-upload"
                    type="file"
                    className="sr-only"
                    accept="image/*,.pdf"
                    onChange={handleFileUpload}
                  />
                </label>
                <p className="pl-1">{t('expenses.form.or_drag_drop')}</p>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                PNG, JPG, PDF up to 10MB
              </p>
            </div>
            {formData.receipt_url && (
              <div className="mt-4">
                <div className="flex items-center justify-center">
                  <div className="flex items-center space-x-2 text-sm text-green-600 dark:text-green-400">
                    <Receipt size={16} />
                    <span>{t('expenses.form.receipt_uploaded')}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Total Display */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {t('expenses.form.total_amount')}:
            </span>
            <span className="text-lg font-bold text-gray-900 dark:text-white">
              {formatCurrency(formData.amount + formData.tax_amount)}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Save size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
            {isEdit ? t('common.update') : t('common.save')}
          </button>
        </div>
      </form>
    </motion.div>
  );
};

export default ExpenseForm;