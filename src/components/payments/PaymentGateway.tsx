import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { CreditCard, Shield, Lock, CheckCircle, AlertCircle } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface PaymentGatewayProps {
  amount: number;
  currency: string;
  description: string;
  onSuccess: (paymentData: any) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

const PaymentGateway: React.FC<PaymentGatewayProps> = ({
  amount,
  currency,
  description,
  onSuccess,
  onError,
  onCancel
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [selectedMethod, setSelectedMethod] = useState('cmi');
  const [processing, setProcessing] = useState(false);
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: ''
  });

  const paymentMethods = [
    {
      id: 'cmi',
      name: 'CMI',
      description: 'Centre Monétique Interbancaire',
      logo: '🏦',
      cards: ['VISA', 'MASTERCARD', 'CMI']
    },
    {
      id: 'payzone',
      name: 'Payzone',
      description: 'Paiement sécurisé Payzone',
      logo: '💳',
      cards: ['VISA', 'MASTERCARD', 'AMEX']
    }
  ];

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency === 'MAD' ? 'EUR' : currency,
      minimumFractionDigits: 2
    }).format(amount).replace('€', 'MAD');
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleCardInputChange = (field: string, value: string) => {
    let formattedValue = value;
    
    if (field === 'number') {
      formattedValue = formatCardNumber(value);
    } else if (field === 'expiry') {
      formattedValue = formatExpiry(value);
    } else if (field === 'cvv') {
      formattedValue = value.replace(/[^0-9]/g, '').substring(0, 4);
    }
    
    setCardData({ ...cardData, [field]: formattedValue });
  };

  const validateCard = () => {
    const { number, expiry, cvv, name } = cardData;
    
    if (!number || number.replace(/\s/g, '').length < 16) {
      return 'Numéro de carte invalide';
    }
    
    if (!expiry || expiry.length < 5) {
      return 'Date d\'expiration invalide';
    }
    
    if (!cvv || cvv.length < 3) {
      return 'Code CVV invalide';
    }
    
    if (!name.trim()) {
      return 'Nom du titulaire requis';
    }
    
    return null;
  };

  const processPayment = async () => {
    const validationError = validateCard();
    if (validationError) {
      onError(validationError);
      return;
    }

    setProcessing(true);

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock payment success
      const paymentResult = {
        transactionId: `TXN_${Date.now()}`,
        method: selectedMethod,
        amount,
        currency,
        status: 'completed',
        timestamp: new Date().toISOString()
      };
      
      onSuccess(paymentResult);
    } catch (error) {
      onError('Erreur lors du traitement du paiement. Veuillez réessayer.');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg max-w-md mx-auto"
    >
      <div className="p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <CreditCard className="text-blue-600 dark:text-blue-400" size={32} />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Paiement sécurisé
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {description}
          </p>
        </div>

        {/* Amount */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-400">Montant à payer:</span>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatAmount(amount, currency)}
            </span>
          </div>
        </div>

        {/* Payment Method Selection */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3">
            Méthode de paiement
          </h4>
          <div className="space-y-3">
            {paymentMethods.map((method) => (
              <label
                key={method.id}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedMethod === method.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <input
                  type="radio"
                  name="paymentMethod"
                  value={method.id}
                  checked={selectedMethod === method.id}
                  onChange={(e) => setSelectedMethod(e.target.value)}
                  className="sr-only"
                />
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{method.logo}</span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {method.name}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {method.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    {method.cards.map((card) => (
                      <span
                        key={card}
                        className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded"
                      >
                        {card}
                      </span>
                    ))}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Card Details Form */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Numéro de carte
            </label>
            <input
              type="text"
              value={cardData.number}
              onChange={(e) => handleCardInputChange('number', e.target.value)}
              placeholder="1234 5678 9012 3456"
              maxLength={19}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date d'expiration
              </label>
              <input
                type="text"
                value={cardData.expiry}
                onChange={(e) => handleCardInputChange('expiry', e.target.value)}
                placeholder="MM/AA"
                maxLength={5}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                CVV
              </label>
              <input
                type="text"
                value={cardData.cvv}
                onChange={(e) => handleCardInputChange('cvv', e.target.value)}
                placeholder="123"
                maxLength={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nom du titulaire
            </label>
            <input
              type="text"
              value={cardData.name}
              onChange={(e) => handleCardInputChange('name', e.target.value)}
              placeholder="AHMED BENALI"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-6">
          <div className="flex items-center">
            <Shield className="text-green-600 dark:text-green-400 mr-2" size={16} />
            <p className="text-sm text-green-800 dark:text-green-400">
              Paiement sécurisé SSL 256-bit. Vos données sont protégées.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={processPayment}
            disabled={processing}
            className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
              processing
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {processing ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Traitement en cours...
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <Lock size={16} className="mr-2" />
                Payer {formatAmount(amount, currency)}
              </div>
            )}
          </button>

          <button
            onClick={onCancel}
            disabled={processing}
            className="w-full py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Annuler
          </button>
        </div>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            En procédant au paiement, vous acceptez nos conditions d'utilisation
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default PaymentGateway;