import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { FileText, Calculator, Sparkles, Globe } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, index }) => {
  const { isRTL } = useLanguage();
  
  return (
    <motion.div 
      className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 flex flex-col items-center text-center"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
    >
      <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">{title}</h3>
      <p className="text-gray-600 dark:text-gray-300">{description}</p>
    </motion.div>
  );
};

const Features: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const features = [
    {
      icon: <FileText size={24} />,
      title: t('landing.features.financial.title'),
      description: t('landing.features.financial.desc')
    },
    {
      icon: <Calculator size={24} />,
      title: t('landing.features.tax.title'),
      description: t('landing.features.tax.desc')
    },
    {
      icon: <Sparkles size={24} />,
      title: t('landing.features.ai.title'),
      description: t('landing.features.ai.desc')
    },
    {
      icon: <Globe size={24} />,
      title: t('landing.features.multilingual.title'),
      description: t('landing.features.multilingual.desc')
    }
  ];
  
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900" id="features">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('landing.features.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('landing.features.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;