import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

const Hero: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-blue-900 to-blue-800 py-24 sm:py-32">
      <div 
        className="absolute inset-0 opacity-20"
        style={{ 
          backgroundImage: 'url("https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260")',
          backgroundPosition: 'center',
          backgroundSize: 'cover'
        }}
      />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.h1 
          className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {t('landing.hero.title')}
        </motion.h1>
        
        <motion.p 
          className="text-xl sm:text-2xl text-blue-100 max-w-3xl mx-auto mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {t('landing.hero.subtitle')}
        </motion.p>
        
        <motion.div 
          className="flex flex-col sm:flex-row justify-center items-center gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
        >
          <a 
            href="#"
            className="inline-flex items-center justify-center px-6 py-3 bg-white text-blue-800 text-base font-medium rounded-md hover:bg-blue-50 transition-colors"
          >
            {t('landing.hero.cta')}
            <ChevronRight size={18} className={isRTL ? 'mr-2 rotate-180' : 'ml-2'} />
          </a>
          
          <a 
            href="#"
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent border-blue-300 text-blue-100 text-base font-medium rounded-md hover:bg-blue-800 transition-colors"
          >
            {t('landing.hero.demo')}
          </a>
        </motion.div>
      </div>
    </div>
  );
};

export default Hero;