import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Check } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import { PricingPlan } from '../../types';

const PricingCard: React.FC<{ plan: PricingPlan; isRTL: boolean }> = ({ plan, isRTL }) => {
  const { t } = useTranslation();
  const { id, name, price, period, description, features, isPopular } = plan;
  
  return (
    <motion.div 
      className={`relative bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden ${isPopular ? 'border-2 border-green-500 dark:border-green-400' : 'border border-gray-200 dark:border-gray-700'}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {isPopular && (
        <div className="absolute top-0 right-0 bg-green-500 text-white text-xs font-semibold px-3 py-1 rounded-bl-lg">
          Popular
        </div>
      )}
      
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{name}</h3>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{description}</p>
        
        <div className="flex items-end mb-6">
          <span className="text-3xl font-bold text-gray-900 dark:text-white">{price}</span>
          <span className="text-gray-500 dark:text-gray-400 ml-1">{period}</span>
        </div>
        
        <ul className="space-y-3 mb-8" style={{ textAlign: isRTL ? 'right' : 'left' }}>
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <span className="flex-shrink-0 text-green-500 dark:text-green-400 mr-2">
                <Check size={18} />
              </span>
              <span className="text-gray-600 dark:text-gray-300">{feature}</span>
            </li>
          ))}
        </ul>
        
        <button 
          className={`w-full py-3 px-4 rounded-md font-medium ${
            isPopular 
              ? 'bg-green-600 hover:bg-green-700 text-white' 
              : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
          } transition-colors`}
        >
          {id === 'free' ? t('landing.pricing.free.cta') : t('landing.pricing.premium.cta')}
        </button>
      </div>
    </motion.div>
  );
};

const Pricing: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  const plans: PricingPlan[] = [
    {
      id: 'free',
      name: t('landing.pricing.free.title'),
      price: 0,
      period: t('landing.pricing.free.period') || '',
      description: t('landing.pricing.free.description'),
      features: [
        t('landing.pricing.free.feature1'),
        t('landing.pricing.free.feature2'),
        t('landing.pricing.free.feature3'),
        t('landing.pricing.free.feature4'),
        t('landing.pricing.free.feature5')
      ]
    },
    {
      id: 'premium',
      name: t('landing.pricing.premium.title'),
      price: 399,
      period: t('landing.pricing.premium.period'),
      description: t('landing.pricing.premium.description'),
      features: [
        t('landing.pricing.premium.feature1'),
        t('landing.pricing.premium.feature2'),
        t('landing.pricing.premium.feature3'),
        t('landing.pricing.premium.feature4'),
        t('landing.pricing.premium.feature5'),
        t('landing.pricing.premium.feature6'),
        t('landing.pricing.premium.feature7')
      ],
      isPopular: true
    }
  ];
  
  return (
    <section className="py-20 bg-white dark:bg-gray-900" id="pricing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('landing.pricing.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('landing.pricing.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {plans.map((plan) => (
            <PricingCard key={plan.id} plan={plan} isRTL={isRTL} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Pricing;