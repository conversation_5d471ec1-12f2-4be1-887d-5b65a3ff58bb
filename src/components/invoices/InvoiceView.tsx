import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Edit, Download, Send, X, Calendar, User, DollarSign, FileText } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import { InvoiceData } from '../../pages/Invoices';

interface InvoiceViewProps {
  invoice: InvoiceData;
  onEdit: () => void;
  onClose: () => void;
}

const InvoiceView: React.FC<InvoiceViewProps> = ({
  invoice,
  onEdit,
  onClose
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number, currency: string = 'MAD') => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency === 'MAD' ? 'EUR' : currency,
      minimumFractionDigits: 2
    }).format(amount).replace('€', 'MAD');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // Implementation for PDF download
    console.log('Download PDF');
  };

  const handleSend = () => {
    // Implementation for sending invoice
    console.log('Send invoice');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-sm"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {t('invoices.view.title')} {invoice.invoice_number}
            </h2>
            <div className="flex items-center mt-2 space-x-4">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                {t(`invoices.status.${invoice.status}`)}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(invoice.invoice_date)}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleSend}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 transition-colors"
            >
              <Send size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              {t('invoices.actions.send')}
            </button>
            
            <button
              onClick={handleDownload}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
            >
              <Download size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              {t('invoices.actions.download')}
            </button>
            
            <button
              onClick={onEdit}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <Edit size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              {t('common.edit')}
            </button>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>
      </div>

      {/* Invoice Content */}
      <div className="p-8" id="invoice-content">
        {/* Company Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                MoroccoERP
              </h1>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <p>123 Avenue Mohammed V</p>
                <p>Casablanca, 20000</p>
                <p>Maroc</p>
                <p>Tél: +*********** 456</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
            
            <div className="text-right">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                {t('invoices.view.invoice')}
              </h2>
              <div className="text-sm space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('invoices.form.number')}:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{invoice.invoice_number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('invoices.form.date')}:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{formatDate(invoice.invoice_date)}</span>
                </div>
                {invoice.due_date && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">{t('invoices.form.due_date')}:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{formatDate(invoice.due_date)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('invoices.view.bill_to')}
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="text-sm space-y-1">
              <p className="font-medium text-gray-900 dark:text-white">
                {invoice.customers?.name || t('invoices.no_customer')}
              </p>
              {invoice.customers?.email && (
                <p className="text-gray-600 dark:text-gray-400">{invoice.customers.email}</p>
              )}
            </div>
          </div>
        </div>

        {/* Invoice Items Table */}
        <div className="mb-8">
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-200 dark:border-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                    {t('invoices.form.description')}
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                    {t('invoices.form.quantity')}
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                    {t('invoices.form.unit_price')}
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                    {t('invoices.form.tax_rate')}
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-600">
                    {t('invoices.form.total')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {/* Sample items - in real implementation, these would come from invoice items */}
                <tr>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                    Consultation en gestion financière
                  </td>
                  <td className="px-4 py-4 text-sm text-center text-gray-900 dark:text-white">
                    10
                  </td>
                  <td className="px-4 py-4 text-sm text-right text-gray-900 dark:text-white">
                    {formatCurrency(500)}
                  </td>
                  <td className="px-4 py-4 text-sm text-center text-gray-900 dark:text-white">
                    20%
                  </td>
                  <td className="px-4 py-4 text-sm text-right font-medium text-gray-900 dark:text-white">
                    {formatCurrency(5000)}
                  </td>
                </tr>
                <tr>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-white">
                    Formation comptabilité
                  </td>
                  <td className="px-4 py-4 text-sm text-center text-gray-900 dark:text-white">
                    2
                  </td>
                  <td className="px-4 py-4 text-sm text-right text-gray-900 dark:text-white">
                    {formatCurrency(1200)}
                  </td>
                  <td className="px-4 py-4 text-sm text-center text-gray-900 dark:text-white">
                    20%
                  </td>
                  <td className="px-4 py-4 text-sm text-right font-medium text-gray-900 dark:text-white">
                    {formatCurrency(2400)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Totals */}
        <div className="flex justify-end mb-8">
          <div className="w-full max-w-sm">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {t('invoices.form.subtotal')}:
                </span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(invoice.subtotal)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {t('invoices.form.tax_total')}:
                </span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(invoice.tax_total)}
                </span>
              </div>
              <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-600">
                <span className="text-base font-semibold text-gray-900 dark:text-white">
                  {t('invoices.form.total')}:
                </span>
                <span className="text-lg font-bold text-gray-900 dark:text-white">
                  {formatCurrency(invoice.total_amount)}
                </span>
              </div>
              {invoice.paid_amount > 0 && (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('invoices.view.paid_amount')}:
                    </span>
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      {formatCurrency(invoice.paid_amount)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600">
                    <span className="text-base font-semibold text-gray-900 dark:text-white">
                      {t('invoices.view.balance_due')}:
                    </span>
                    <span className="text-lg font-bold text-red-600 dark:text-red-400">
                      {formatCurrency(invoice.total_amount - invoice.paid_amount)}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Notes */}
        {invoice.notes && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              {t('invoices.form.notes')}
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {invoice.notes}
              </p>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>{t('invoices.view.footer_text')}</p>
            <p className="mt-2">
              {t('invoices.view.payment_terms')}
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default InvoiceView;