import React from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, User, Filter } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface InvoiceFiltersProps {
  filters: {
    status: string;
    dateFrom: string;
    dateTo: string;
    customer: string;
  };
  onFiltersChange: (filters: any) => void;
}

const InvoiceFilters: React.FC<InvoiceFiltersProps> = ({
  filters,
  onFiltersChange
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      status: '',
      dateFrom: '',
      dateTo: '',
      customer: ''
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Filter size={18} className={`text-gray-500 dark:text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {t('invoices.filters.title')}
          </h3>
        </div>
        <button
          onClick={clearFilters}
          className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
        >
          {t('invoices.filters.clear')}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('invoices.filters.status')}
          </label>
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value="">{t('invoices.filters.all_statuses')}</option>
            <option value="draft">{t('invoices.status.draft')}</option>
            <option value="sent">{t('invoices.status.sent')}</option>
            <option value="paid">{t('invoices.status.paid')}</option>
            <option value="overdue">{t('invoices.status.overdue')}</option>
            <option value="cancelled">{t('invoices.status.cancelled')}</option>
          </select>
        </div>

        {/* Date From Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Calendar size={16} className="inline mr-1" />
            {t('invoices.filters.date_from')}
          </label>
          <input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
          />
        </div>

        {/* Date To Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Calendar size={16} className="inline mr-1" />
            {t('invoices.filters.date_to')}
          </label>
          <input
            type="date"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
          />
        </div>

        {/* Customer Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <User size={16} className="inline mr-1" />
            {t('invoices.filters.customer')}
          </label>
          <input
            type="text"
            value={filters.customer}
            onChange={(e) => handleFilterChange('customer', e.target.value)}
            placeholder={t('invoices.filters.customer_placeholder')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
          />
        </div>
      </div>
    </div>
  );
};

export default InvoiceFilters;