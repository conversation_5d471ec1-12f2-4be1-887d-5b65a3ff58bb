import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  MapPin,
  Building,
  Truck,
  Download,
  Upload,
  Star,
  Package,
  Clock,
} from "lucide-react";
import Header from "../components/common/Header";
import { useAuth } from "../hooks/useAuth";
import { supabase } from "../lib/supabase";

interface Supplier {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  address: any | null;
  tax_id: string | null; // Identifiant Fiscal (IF)
  ice_number: string | null; // Identifiant Commun de l'Entreprise (ICE)
  rc_number: string | null; // Registre de Commerce (RC)
  contact_person: string | null;
  category: string;
  payment_terms: string;
  rating: number;
  is_active: boolean;
  notes: string | null;
  bank_details: any | null; // RIB (Relevé d'Identité Bancaire)
  certification: string | null; // ISO, Halal, etc.
  created_at: string;
  updated_at: string;
}

// Moroccan-specific supplier categories
const SUPPLIER_CATEGORIES = [
  "Matières Premières", // Raw Materials
  "Services Professionnels", // Professional Services
  "Équipements Industriels", // Industrial Equipment
  "Fournitures de Bureau", // Office Supplies
  "Technologies & IT", // Technology & IT
  "Services Publics", // Utilities
  "Transport & Logistique", // Transportation & Logistics
  "Construction & BTP", // Construction & Public Works
  "Textile & Cuir", // Textile & Leather
  "Agroalimentaire", // Agri-food
  "Pharmaceutique", // Pharmaceutical
  "Automobile", // Automotive
  "Énergie Renouvelable", // Renewable Energy
  "Services Financiers", // Financial Services
  "Autre", // Other
];

// Moroccan business payment terms
const PAYMENT_TERMS = [
  "Comptant", // Cash
  "Net 15 jours", // Net 15 days
  "Net 30 jours", // Net 30 days
  "Net 60 jours", // Net 60 days
  "Net 90 jours", // Net 90 days
  "Fin de mois", // End of month
  "Contre remboursement", // Cash on delivery
  "Avance requise", // Prepayment required
  "Crédit documentaire", // Documentary credit
  "Virement bancaire", // Bank transfer
];

const Suppliers: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();

  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(
    null
  );
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);

  // Load suppliers
  useEffect(() => {
    loadSuppliers();
  }, [user]);

  const loadSuppliers = async () => {
    if (!user?.tenant_id) return;

    try {
      setLoading(true);

      // For now, since suppliers table doesn't exist, we'll show empty state
      // This will be replaced once we create the proper database schema
      console.log("Suppliers table not yet created - showing empty state");
      setSuppliers([]);

      // Uncomment this when suppliers table is created:
      /*
      const { data, error } = await supabase
        .from("suppliers")
        .select("*")
        .eq("tenant_id", user.tenant_id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error loading suppliers:", error);
        setSuppliers([]);
        return;
      }

      // Transform data to match our interface
      const transformedData = (data || []).map(supplier => ({
        ...supplier,
        address: supplier.address || null,
        tax_id: supplier.tax_id || null,
        ice_number: supplier.ice_number || null,
        rc_number: supplier.rc_number || null,
        contact_person: supplier.contact_person || null,
        bank_details: supplier.bank_details || null,
        certification: supplier.certification || null,
        notes: supplier.notes || null,
        is_active: supplier.is_active !== undefined ? supplier.is_active : true,
        rating: supplier.rating || 3,
      }));

      setSuppliers(transformedData);
      */
    } catch (error) {
      console.error("Error loading suppliers:", error);
      setSuppliers([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter suppliers based on search term and category
  const filteredSuppliers = suppliers.filter((supplier) => {
    const matchesSearch =
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.phone?.includes(searchTerm) ||
      supplier.contact_person
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      supplier.category.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      !categoryFilter || supplier.category === categoryFilter;

    return matchesSearch && matchesCategory;
  });

  const handleCreateSupplier = () => {
    setSelectedSupplier(null);
    setShowCreateModal(true);
  };

  const handleEditSupplier = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setShowEditModal(true);
  };

  const handleViewSupplier = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setShowViewModal(true);
  };

  const handleDeleteSupplier = async (supplier: Supplier) => {
    if (!confirm(`Are you sure you want to delete ${supplier.name}?`)) return;

    try {
      const { error } = await supabase
        .from("suppliers")
        .delete()
        .eq("id", supplier.id);

      if (error) throw error;

      // Reload suppliers
      loadSuppliers();
    } catch (error) {
      console.error("Error deleting supplier:", error);
      alert("Failed to delete supplier");
    }
  };

  const formatAddress = (address: any) => {
    if (!address) return "No address";
    if (typeof address === "string") return address;

    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.postal_code) parts.push(address.postal_code);
    if (address.country) parts.push(address.country);

    return parts.join(", ") || "No address";
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating
            ? "text-yellow-400 fill-current"
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  // Redirect to login if not authenticated
  if (!authLoading && !user) {
    navigate("/auth/login");
    return null;
  }

  // Show loading while auth is loading
  if (authLoading || loading) {
    return (
      <div>
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Suppliers
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Manage your supplier relationships
            </p>
          </div>

          <div className="flex space-x-3 mt-4 md:mt-0">
            <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </button>
            <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
            <button
              onClick={handleCreateSupplier}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Supplier
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search suppliers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Categories</option>
            {SUPPLIER_CATEGORIES.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
            <Filter className="h-4 w-4 mr-2" />
            More Filters
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Truck className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Suppliers
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {suppliers.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Active Suppliers
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {suppliers.filter((s) => s.is_active).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Star className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Avg Rating
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {suppliers.length > 0
                    ? (
                        suppliers.reduce((sum, s) => sum + s.rating, 0) /
                        suppliers.length
                      ).toFixed(1)
                    : "0.0"}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Categories
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {new Set(suppliers.map((s) => s.category)).size}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Suppliers Table */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Supplier
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredSuppliers.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <Truck className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                        No suppliers
                      </h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Get started by creating your first supplier.
                      </p>
                      <div className="mt-6">
                        <button
                          onClick={handleCreateSupplier}
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Supplier
                        </button>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredSuppliers.map((supplier) => (
                    <motion.tr
                      key={supplier.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {supplier.name}
                          </div>
                          {supplier.contact_person && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Contact: {supplier.contact_person}
                            </div>
                          )}
                          {supplier.tax_id && (
                            <div className="text-xs text-gray-400">
                              Tax ID: {supplier.tax_id}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          {supplier.email && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <Mail className="h-3 w-3 mr-1" />
                              {supplier.email}
                            </div>
                          )}
                          {supplier.phone && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <Phone className="h-3 w-3 mr-1" />
                              {supplier.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                          {supplier.category}
                        </span>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {supplier.payment_terms}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-1">
                          {renderStars(supplier.rating)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            supplier.is_active
                              ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                              : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                          }`}
                        >
                          {supplier.is_active ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewSupplier(supplier)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditSupplier(supplier)}
                            className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteSupplier(supplier)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      {/* Create/Edit Supplier Modal */}
      {(showCreateModal || showEditModal) && (
        <SupplierFormModal
          supplier={selectedSupplier}
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedSupplier(null);
          }}
          onSave={() => {
            loadSuppliers();
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedSupplier(null);
          }}
        />
      )}

      {/* View Supplier Modal */}
      {showViewModal && selectedSupplier && (
        <SupplierViewModal
          supplier={selectedSupplier}
          isOpen={showViewModal}
          onClose={() => {
            setShowViewModal(false);
            setSelectedSupplier(null);
          }}
          onEdit={() => {
            setShowViewModal(false);
            setShowEditModal(true);
          }}
        />
      )}
    </div>
  );
};

// Supplier Form Modal Component with Moroccan-specific fields
interface SupplierFormModalProps {
  supplier: Supplier | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const SupplierFormModal: React.FC<SupplierFormModalProps> = ({
  supplier,
  isOpen,
  onClose,
  onSave,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    contact_person: "",
    tax_id: "", // IF
    ice_number: "", // ICE
    rc_number: "", // RC
    category: "Services Professionnels",
    payment_terms: "Net 30 jours",
    rating: 3,
    street: "",
    city: "",
    postal_code: "",
    country: "Maroc",
    is_active: true,
    notes: "",
    bank_name: "",
    bank_account: "",
    rib_key: "",
    certification: "",
  });

  useEffect(() => {
    if (supplier) {
      setFormData({
        name: supplier.name || "",
        email: supplier.email || "",
        phone: supplier.phone || "",
        contact_person: supplier.contact_person || "",
        tax_id: supplier.tax_id || "",
        ice_number: supplier.ice_number || "",
        rc_number: supplier.rc_number || "",
        category: supplier.category || "Services Professionnels",
        payment_terms: supplier.payment_terms || "Net 30 jours",
        rating: supplier.rating || 3,
        street: supplier.address?.street || "",
        city: supplier.address?.city || "",
        postal_code: supplier.address?.postal_code || "",
        country: supplier.address?.country || "Maroc",
        is_active: supplier.is_active,
        notes: supplier.notes || "",
        bank_name: supplier.bank_details?.bank_name || "",
        bank_account: supplier.bank_details?.account_number || "",
        rib_key: supplier.bank_details?.rib_key || "",
        certification: supplier.certification || "",
      });
    } else {
      setFormData({
        name: "",
        email: "",
        phone: "",
        contact_person: "",
        tax_id: "",
        ice_number: "",
        rc_number: "",
        category: "Services Professionnels",
        payment_terms: "Net 30 jours",
        rating: 3,
        street: "",
        city: "",
        postal_code: "",
        country: "Maroc",
        is_active: true,
        notes: "",
        bank_name: "",
        bank_account: "",
        rib_key: "",
        certification: "",
      });
    }
  }, [supplier]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.tenant_id) return;

    // Temporary: Show alert that suppliers table needs to be created
    alert(
      "Suppliers functionality will be available once the database schema is set up. For now, you can use the Customers page which is fully functional."
    );
    return;

    // This code will be enabled once the suppliers table is created
    /*
    setLoading(true);
    try {
      const supplierData = {
        name: formData.name,
        email: formData.email || null,
        phone: formData.phone || null,
        contact_person: formData.contact_person || null,
        tax_id: formData.tax_id || null,
        ice_number: formData.ice_number || null,
        rc_number: formData.rc_number || null,
        category: formData.category,
        payment_terms: formData.payment_terms,
        rating: formData.rating,
        address: {
          street: formData.street,
          city: formData.city,
          postal_code: formData.postal_code,
          country: formData.country,
        },
        bank_details: {
          bank_name: formData.bank_name,
          account_number: formData.bank_account,
          rib_key: formData.rib_key,
        },
        certification: formData.certification || null,
        is_active: formData.is_active,
        notes: formData.notes || null,
        tenant_id: user.tenant_id,
      };

      if (supplier) {
        // Update existing supplier
        const { error } = await supabase
          .from("suppliers")
          .update(supplierData)
          .eq("id", supplier.id);

        if (error) throw error;
      } else {
        // Create new supplier
        const { error } = await supabase
          .from("suppliers")
          .insert([supplierData]);

        if (error) throw error;
      }

      onSave();
    } catch (error) {
      console.error("Error saving supplier:", error);
      alert("Failed to save supplier");
    } finally {
      setLoading(false);
    }
    */
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
            {supplier ? "Modifier Fournisseur" : "Nouveau Fournisseur"}
          </h3>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Nom de l'entreprise *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Personne de contact
                </label>
                <input
                  type="text"
                  value={formData.contact_person}
                  onChange={(e) =>
                    setFormData({ ...formData, contact_person: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Téléphone
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) =>
                    setFormData({ ...formData, phone: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="+212 5XX XXX XXX"
                />
              </div>
            </div>

            {/* Moroccan Business Identifiers */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Identifiants Officiels
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Identifiant Fiscal (IF)
                  </label>
                  <input
                    type="text"
                    value={formData.tax_id}
                    onChange={(e) =>
                      setFormData({ ...formData, tax_id: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="12345678"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    ICE (Identifiant Commun de l'Entreprise)
                  </label>
                  <input
                    type="text"
                    value={formData.ice_number}
                    onChange={(e) =>
                      setFormData({ ...formData, ice_number: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="000123456000012"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Registre de Commerce (RC)
                  </label>
                  <input
                    type="text"
                    value={formData.rc_number}
                    onChange={(e) =>
                      setFormData({ ...formData, rc_number: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="12345"
                  />
                </div>
              </div>
            </div>

            {/* Business Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Catégorie
                </label>
                <select
                  value={formData.category}
                  onChange={(e) =>
                    setFormData({ ...formData, category: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {SUPPLIER_CATEGORIES.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Conditions de paiement
                </label>
                <select
                  value={formData.payment_terms}
                  onChange={(e) =>
                    setFormData({ ...formData, payment_terms: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {PAYMENT_TERMS.map((term) => (
                    <option key={term} value={term}>
                      {term}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Évaluation (1-5 étoiles)
                </label>
                <select
                  value={formData.rating}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      rating: parseInt(e.target.value),
                    })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <option key={rating} value={rating}>
                      {rating} étoile{rating > 1 ? "s" : ""}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Address */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Adresse
              </h4>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Adresse
                </label>
                <input
                  type="text"
                  value={formData.street}
                  onChange={(e) =>
                    setFormData({ ...formData, street: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Ville
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) =>
                      setFormData({ ...formData, city: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Casablanca, Rabat, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Code postal
                  </label>
                  <input
                    type="text"
                    value={formData.postal_code}
                    onChange={(e) =>
                      setFormData({ ...formData, postal_code: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="20000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Pays
                  </label>
                  <select
                    value={formData.country}
                    onChange={(e) =>
                      setFormData({ ...formData, country: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="Maroc">Maroc</option>
                    <option value="France">France</option>
                    <option value="Espagne">Espagne</option>
                    <option value="Tunisie">Tunisie</option>
                    <option value="Algérie">Algérie</option>
                    <option value="Autre">Autre</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Bank Details (RIB) */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Coordonnées Bancaires (RIB)
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Banque
                  </label>
                  <input
                    type="text"
                    value={formData.bank_name}
                    onChange={(e) =>
                      setFormData({ ...formData, bank_name: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Attijariwafa Bank, BMCE, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Numéro de compte
                  </label>
                  <input
                    type="text"
                    value={formData.bank_account}
                    onChange={(e) =>
                      setFormData({ ...formData, bank_account: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="007 780 ************* 12"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Clé RIB
                  </label>
                  <input
                    type="text"
                    value={formData.rib_key}
                    onChange={(e) =>
                      setFormData({ ...formData, rib_key: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="12"
                  />
                </div>
              </div>
            </div>

            {/* Certification */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Certifications (ISO, Halal, etc.)
              </label>
              <input
                type="text"
                value={formData.certification}
                onChange={(e) =>
                  setFormData({ ...formData, certification: e.target.value })
                }
                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="ISO 9001, Halal, Bio, etc."
              />
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Notes
              </label>
              <textarea
                rows={3}
                value={formData.notes}
                onChange={(e) =>
                  setFormData({ ...formData, notes: e.target.value })
                }
                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Notes internes sur le fournisseur..."
              />
            </div>

            {/* Status */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) =>
                  setFormData({ ...formData, is_active: e.target.checked })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="is_active"
                className="ml-2 block text-sm text-gray-900 dark:text-white"
              >
                Fournisseur actif
              </label>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading
                  ? "Enregistrement..."
                  : supplier
                  ? "Modifier"
                  : "Créer"}
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

// Supplier View Modal Component
interface SupplierViewModalProps {
  supplier: Supplier;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
}

const SupplierViewModal: React.FC<SupplierViewModalProps> = ({
  supplier,
  isOpen,
  onClose,
  onEdit,
}) => {
  if (!isOpen) return null;

  const formatAddress = (address: any) => {
    if (!address) return "Aucune adresse fournie";
    if (typeof address === "string") return address;

    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.postal_code) parts.push(address.postal_code);
    if (address.country) parts.push(address.country);

    return parts.join(", ") || "Aucune adresse fournie";
  };

  const formatBankDetails = (bankDetails: any) => {
    if (!bankDetails) return "Aucune information bancaire";

    const parts = [];
    if (bankDetails.bank_name) parts.push(bankDetails.bank_name);
    if (bankDetails.account_number)
      parts.push(`Compte: ${bankDetails.account_number}`);
    if (bankDetails.rib_key) parts.push(`Clé: ${bankDetails.rib_key}`);

    return parts.join(" - ") || "Aucune information bancaire";
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating
            ? "text-yellow-400 fill-current"
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Détails du Fournisseur
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={onEdit}
                className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Edit className="h-4 w-4 mr-1" />
                Modifier
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>
          </div>

          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Informations Générales
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Nom de l'entreprise
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.name}
                  </p>
                </div>
                {supplier.contact_person && (
                  <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                      Personne de contact
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {supplier.contact_person}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Informations de Contact
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Email
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.email || "Non fourni"}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Téléphone
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.phone || "Non fourni"}
                  </p>
                </div>
              </div>
            </div>

            {/* Moroccan Business Identifiers */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Identifiants Officiels
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Identifiant Fiscal (IF)
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.tax_id || "Non fourni"}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    ICE
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.ice_number || "Non fourni"}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Registre de Commerce (RC)
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.rc_number || "Non fourni"}
                  </p>
                </div>
              </div>
            </div>

            {/* Business Details */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Détails Commerciaux
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Catégorie
                  </label>
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                    {supplier.category}
                  </span>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Conditions de paiement
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {supplier.payment_terms}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Évaluation
                  </label>
                  <div className="flex items-center space-x-1">
                    {renderStars(supplier.rating)}
                    <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                      ({supplier.rating}/5)
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Address */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Adresse
              </h4>
              <p className="text-sm text-gray-900 dark:text-white">
                {formatAddress(supplier.address)}
              </p>
            </div>

            {/* Bank Details */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Coordonnées Bancaires (RIB)
              </h4>
              <p className="text-sm text-gray-900 dark:text-white">
                {formatBankDetails(supplier.bank_details)}
              </p>
            </div>

            {/* Certification */}
            {supplier.certification && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Certifications
                </h4>
                <p className="text-sm text-gray-900 dark:text-white">
                  {supplier.certification}
                </p>
              </div>
            )}

            {/* Notes */}
            {supplier.notes && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Notes
                </h4>
                <p className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                  {supplier.notes}
                </p>
              </div>
            )}

            {/* Status */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Statut
              </h4>
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  supplier.is_active
                    ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                }`}
              >
                {supplier.is_active ? "Actif" : "Inactif"}
              </span>
            </div>

            {/* Timestamps */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Historique
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Créé le
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {new Date(supplier.created_at).toLocaleDateString("fr-FR")}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    Dernière modification
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {new Date(supplier.updated_at).toLocaleDateString("fr-FR")}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Suppliers;
