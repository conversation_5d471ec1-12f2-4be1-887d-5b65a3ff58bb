import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import OnboardingOrchestrator from '../components/auth/OnboardingOrchestrator';

const Onboarding: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleOnboardingComplete = async (userData: any) => {
    try {
      // Here you would typically save the onboarding data to your backend
      console.log('Onboarding completed with data:', userData);
      
      // For now, we'll just store it in localStorage as a demo
      localStorage.setItem('onboarding_data', JSON.stringify({
        ...userData,
        completedAt: new Date().toISOString(),
        userId: user?.id
      }));

      // Mark onboarding as completed for this user
      localStorage.setItem('onboarding_completed', 'true');

      // If the user wants company formation, we might want to redirect to a special page
      if (userData.wantsFormation) {
        // Could redirect to a "formation in progress" page
        navigate('/dashboard?formation=pending');
      } else {
        // Regular redirect to dashboard
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Error saving onboarding data:', error);
      // Even if there's an error, we should still let the user proceed
      navigate('/dashboard');
    }
  };

  return <OnboardingOrchestrator onComplete={handleOnboardingComplete} />;
};

export default Onboarding;
