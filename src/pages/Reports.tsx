import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BarChart3, TrendingUp, DollarSign, Calendar, Download, Filter, Eye } from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '../components/common/Header';
import { useLanguage } from '../context/LanguageContext';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

const Reports: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedReport, setSelectedReport] = useState('overview');

  // Mock data for reports
  const revenueData = [
    { month: 'Jan', revenue: 45000, expenses: 32000, profit: 13000 },
    { month: 'Feb', revenue: 52000, expenses: 35000, profit: 17000 },
    { month: 'Mar', revenue: 48000, expenses: 33000, profit: 15000 },
    { month: 'Apr', revenue: 61000, expenses: 38000, profit: 23000 },
    { month: 'May', revenue: 55000, expenses: 36000, profit: 19000 },
    { month: 'Jun', revenue: 67000, expenses: 41000, profit: 26000 },
  ];

  const expenseBreakdown = [
    { name: 'Fournitures bureau', value: 15000, color: '#3B82F6' },
    { name: 'Loyer', value: 25000, color: '#10B981' },
    { name: 'Télécommunications', value: 8000, color: '#F59E0B' },
    { name: 'Marketing', value: 12000, color: '#EF4444' },
    { name: 'Autres', value: 10000, color: '#8B5CF6' },
  ];

  const taxData = [
    { type: 'TVA 20%', amount: 12000, status: 'paid' },
    { type: 'TVA 14%', amount: 3500, status: 'pending' },
    { type: 'Impôt professionnel', amount: 8000, status: 'due' },
    { type: 'Cotisations sociales', amount: 15000, status: 'paid' },
  ];

  const reportTypes = [
    { id: 'overview', name: 'Vue d\'ensemble', icon: <BarChart3 size={20} /> },
    { id: 'profit-loss', name: 'Compte de résultat', icon: <TrendingUp size={20} /> },
    { id: 'cash-flow', name: 'Flux de trésorerie', icon: <DollarSign size={20} /> },
    { id: 'tax-summary', name: 'Résumé fiscal', icon: <Calendar size={20} /> },
  ];

  const periods = [
    { value: 'week', label: 'Cette semaine' },
    { value: 'month', label: 'Ce mois' },
    { value: 'quarter', label: 'Ce trimestre' },
    { value: 'year', label: 'Cette année' },
  ];

  const renderOverviewReport = () => (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Chiffre d'affaires</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">328,000 MAD</p>
              <p className="text-sm text-green-600">+12.5% vs mois dernier</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <TrendingUp className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Dépenses</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">215,000 MAD</p>
              <p className="text-sm text-red-600">+5.2% vs mois dernier</p>
            </div>
            <div className="p-3 bg-red-100 dark:bg-red-900 rounded-full">
              <DollarSign className="text-red-600 dark:text-red-400" size={24} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Bénéfice net</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">113,000 MAD</p>
              <p className="text-sm text-green-600">+18.3% vs mois dernier</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <BarChart3 className="text-green-600 dark:text-green-400" size={24} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Marge bénéficiaire</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">34.5%</p>
              <p className="text-sm text-green-600">+2.1% vs mois dernier</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
              <Calendar className="text-purple-600 dark:text-purple-400" size={24} />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Revenue Trend Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Évolution du chiffre d'affaires
        </h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="revenue" fill="#3B82F6" name="Chiffre d'affaires" />
              <Bar dataKey="expenses" fill="#EF4444" name="Dépenses" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Expense Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Répartition des dépenses
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={expenseBreakdown}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {expenseBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Résumé fiscal
          </h3>
          <div className="space-y-4">
            {taxData.map((tax, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">{tax.type}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {tax.amount.toLocaleString()} MAD
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                  tax.status === 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                  tax.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                }`}>
                  {tax.status === 'paid' ? 'Payé' : tax.status === 'pending' ? 'En attente' : 'À payer'}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderProfitLossReport = () => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        Compte de résultat - {selectedPeriod === 'month' ? 'Juin 2025' : 'Période sélectionnée'}
      </h3>
      
      <div className="space-y-4">
        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">PRODUITS</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Ventes de marchandises</span>
              <span className="font-medium">450,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Prestations de services</span>
              <span className="font-medium">180,000 MAD</span>
            </div>
            <div className="flex justify-between font-semibold text-lg border-t pt-2">
              <span>Total des produits</span>
              <span>630,000 MAD</span>
            </div>
          </div>
        </div>

        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">CHARGES</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Achats de marchandises</span>
              <span className="font-medium">280,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Charges de personnel</span>
              <span className="font-medium">120,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Charges externes</span>
              <span className="font-medium">85,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Impôts et taxes</span>
              <span className="font-medium">25,000 MAD</span>
            </div>
            <div className="flex justify-between font-semibold text-lg border-t pt-2">
              <span>Total des charges</span>
              <span>510,000 MAD</span>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-xl font-bold text-green-800 dark:text-green-400">RÉSULTAT NET</span>
            <span className="text-2xl font-bold text-green-800 dark:text-green-400">120,000 MAD</span>
          </div>
          <p className="text-sm text-green-600 dark:text-green-400 mt-1">
            Marge bénéficiaire: 19.05%
          </p>
        </div>
      </div>
    </div>
  );

  const renderCashFlowReport = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Flux de trésorerie
        </h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="profit" stroke="#10B981" strokeWidth={3} name="Flux net" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Encaissements</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Ventes comptant</span>
              <span className="font-medium text-green-600">320,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Règlements clients</span>
              <span className="font-medium text-green-600">180,000 MAD</span>
            </div>
            <div className="flex justify-between font-semibold border-t pt-2">
              <span>Total encaissements</span>
              <span className="text-green-600">500,000 MAD</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Décaissements</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Fournisseurs</span>
              <span className="font-medium text-red-600">250,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Charges</span>
              <span className="font-medium text-red-600">120,000 MAD</span>
            </div>
            <div className="flex justify-between font-semibold border-t pt-2">
              <span>Total décaissements</span>
              <span className="text-red-600">370,000 MAD</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Solde</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Solde initial</span>
              <span className="font-medium">50,000 MAD</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Flux net</span>
              <span className="font-medium text-green-600">130,000 MAD</span>
            </div>
            <div className="flex justify-between font-semibold border-t pt-2">
              <span>Solde final</span>
              <span className="text-blue-600">180,000 MAD</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTaxSummaryReport = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          Résumé fiscal - Juin 2025
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">TVA</h4>
            <div className="space-y-3">
              <div className="flex justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span>TVA collectée (20%)</span>
                <span className="font-medium">90,000 MAD</span>
              </div>
              <div className="flex justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span>TVA déductible</span>
                <span className="font-medium">35,000 MAD</span>
              </div>
              <div className="flex justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <span className="font-semibold">TVA à payer</span>
                <span className="font-bold text-blue-600">55,000 MAD</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Autres taxes</h4>
            <div className="space-y-3">
              <div className="flex justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span>Impôt professionnel</span>
                <span className="font-medium">8,000 MAD</span>
              </div>
              <div className="flex justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span>Cotisations sociales</span>
                <span className="font-medium">15,000 MAD</span>
              </div>
              <div className="flex justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span>Taxe de formation</span>
                <span className="font-medium">2,500 MAD</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <h5 className="font-semibold text-yellow-800 dark:text-yellow-400 mb-2">
            Échéances à venir
          </h5>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Déclaration TVA - Juillet</span>
              <span className="font-medium">20 Août 2025</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Impôt professionnel Q3</span>
              <span className="font-medium">31 Octobre 2025</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderReport = () => {
    switch (selectedReport) {
      case 'profit-loss':
        return renderProfitLossReport();
      case 'cash-flow':
        return renderCashFlowReport();
      case 'tax-summary':
        return renderTaxSummaryReport();
      default:
        return renderOverviewReport();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('nav.reports')}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Analysez vos performances et suivez vos indicateurs clés
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
            >
              {periods.map((period) => (
                <option key={period.value} value={period.value}>
                  {period.label}
                </option>
              ))}
            </select>
            
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
              <Download size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              Exporter
            </button>
          </div>
        </div>

        {/* Report Type Selector */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {reportTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setSelectedReport(type.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    selectedReport === type.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  {type.icon}
                  {type.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Report Content */}
        <motion.div
          key={selectedReport}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {renderReport()}
        </motion.div>
      </main>
    </div>
  );
};

export default Reports;