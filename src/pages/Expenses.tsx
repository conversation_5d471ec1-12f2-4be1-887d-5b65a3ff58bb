import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, Search, Filter, Upload, Camera, FileText, Eye, Edit, Trash2, Receipt } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from '../components/common/Header';
import ExpenseList from '../components/expenses/ExpenseList';
import ExpenseForm from '../components/expenses/ExpenseForm';
import ExpenseView from '../components/expenses/ExpenseView';
import ExpenseFilters from '../components/expenses/ExpenseFilters';
import ReceiptUpload from '../components/expenses/ReceiptUpload';
import { useExpenses } from '../hooks/useExpenses';
import { useLanguage } from '../context/LanguageContext';

export type ExpenseStatus = 'draft' | 'pending' | 'approved' | 'rejected' | 'paid';

export interface ExpenseData {
  id: string;
  expense_number: string | null;
  expense_date: string;
  category_id: string | null;
  description: string;
  amount: number;
  tax_amount: number;
  currency: string;
  receipt_url: string | null;
  status: ExpenseStatus;
  suppliers?: {
    name: string;
  };
  expense_categories?: {
    name: string;
  };
}

type ViewMode = 'list' | 'create' | 'edit' | 'view' | 'upload';

const Expenses: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { expenses, loading, createExpense, updateExpense, deleteExpense } = useExpenses();
  
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedExpense, setSelectedExpense] = useState<ExpenseData | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    dateFrom: '',
    dateTo: '',
    category: '',
  });

  const handleCreateExpense = () => {
    setSelectedExpense(null);
    setViewMode('create');
  };

  const handleUploadReceipt = () => {
    setViewMode('upload');
  };

  const handleEditExpense = (expense: ExpenseData) => {
    setSelectedExpense(expense);
    setViewMode('edit');
  };

  const handleViewExpense = (expense: ExpenseData) => {
    setSelectedExpense(expense);
    setViewMode('view');
  };

  const handleDeleteExpense = async (expenseId: string) => {
    if (window.confirm(t('expenses.delete.confirm'))) {
      try {
        await deleteExpense(expenseId);
      } catch (error) {
        console.error('Error deleting expense:', error);
      }
    }
  };

  const handleSaveExpense = async (expenseData: Partial<ExpenseData>) => {
    try {
      if (viewMode === 'create') {
        await createExpense(expenseData);
      } else if (viewMode === 'edit' && selectedExpense) {
        await updateExpense(selectedExpense.id, expenseData);
      }
      setViewMode('list');
      setSelectedExpense(null);
    } catch (error) {
      console.error('Error saving expense:', error);
    }
  };

  const handleReceiptProcessed = (extractedData: any) => {
    setSelectedExpense(extractedData);
    setViewMode('create');
  };

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.suppliers?.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !filters.status || expense.status === filters.status;
    const matchesCategory = !filters.category || expense.expense_categories?.name.toLowerCase().includes(filters.category.toLowerCase());
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusColor = (status: ExpenseStatus) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'paid': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const renderContent = () => {
    switch (viewMode) {
      case 'create':
      case 'edit':
        return (
          <ExpenseForm
            expense={selectedExpense}
            onSave={handleSaveExpense}
            onCancel={() => setViewMode('list')}
            isEdit={viewMode === 'edit'}
          />
        );
      case 'view':
        return selectedExpense ? (
          <ExpenseView
            expense={selectedExpense}
            onEdit={() => setViewMode('edit')}
            onClose={() => setViewMode('list')}
          />
        ) : null;
      case 'upload':
        return (
          <ReceiptUpload
            onReceiptProcessed={handleReceiptProcessed}
            onCancel={() => setViewMode('list')}
          />
        );
      default:
        return (
          <div className="space-y-6">
            {/* Header Actions */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t('nav.expenses')}
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  {t('expenses.subtitle')}
                </p>
              </div>
              
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  <Filter size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('common.filter')}
                </button>
                
                <button
                  onClick={handleUploadReceipt}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors"
                >
                  <Receipt size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('expenses.upload_receipt')}
                </button>
                
                <button
                  onClick={handleCreateExpense}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                  {t('expenses.create')}
                </button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="space-y-4">
              <div className="relative">
                <Search size={20} className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`} />
                <input
                  type="text"
                  placeholder={t('expenses.search.placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white`}
                />
              </div>

              <AnimatePresence>
                {showFilters && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ExpenseFilters
                      filters={filters}
                      onFiltersChange={setFilters}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Expense List */}
            <ExpenseList
              expenses={filteredExpenses}
              loading={loading}
              onView={handleViewExpense}
              onEdit={handleEditExpense}
              onDelete={handleDeleteExpense}
              getStatusColor={getStatusColor}
            />
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={viewMode}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderContent()}
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
};

export default Expenses;