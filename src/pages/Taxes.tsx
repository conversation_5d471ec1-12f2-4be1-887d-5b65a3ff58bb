import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Calculator, Calendar, AlertTriangle, CheckCircle, Clock, FileText, Download } from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '../components/common/Header';
import { useLanguage } from '../context/LanguageContext';

const Taxes: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [selectedTaxType, setSelectedTaxType] = useState('vat');

  // Mock data for tax obligations
  const taxObligations = [
    {
      id: '1',
      type: 'TVA',
      period: 'Juin 2025',
      amount: 55000,
      dueDate: '2025-07-20',
      status: 'pending',
      description: 'Déclaration mensuelle TVA'
    },
    {
      id: '2',
      type: 'Impôt professionnel',
      period: 'T2 2025',
      amount: 8000,
      dueDate: '2025-07-31',
      status: 'due',
      description: 'Déclaration trimestrielle'
    },
    {
      id: '3',
      type: 'Cotisations sociales',
      period: 'Juin 2025',
      amount: 15000,
      dueDate: '2025-07-15',
      status: 'paid',
      description: 'CNSS - Employés'
    },
    {
      id: '4',
      type: 'TVA',
      period: 'Mai 2025',
      amount: 48000,
      dueDate: '2025-06-20',
      status: 'paid',
      description: 'Déclaration mensuelle TVA'
    }
  ];

  const taxRates = [
    { type: 'TVA Standard', rate: '20%', description: 'Taux normal pour biens et services' },
    { type: 'TVA Réduit 1', rate: '14%', description: 'Services financiers et bancaires' },
    { type: 'TVA Réduit 2', rate: '10%', description: 'Services touristiques et hôteliers' },
    { type: 'TVA Réduit 3', rate: '7%', description: 'Produits de première nécessité' },
    { type: 'TVA Exonéré', rate: '0%', description: 'Exportations et certains services' }
  ];

  const complianceCalendar = [
    {
      date: '2025-07-15',
      title: 'Cotisations sociales',
      type: 'deadline',
      status: 'upcoming'
    },
    {
      date: '2025-07-20',
      title: 'Déclaration TVA',
      type: 'deadline',
      status: 'upcoming'
    },
    {
      date: '2025-07-31',
      title: 'Impôt professionnel',
      type: 'deadline',
      status: 'upcoming'
    },
    {
      date: '2025-08-20',
      title: 'Déclaration TVA',
      type: 'deadline',
      status: 'future'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'due': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle size={16} className="text-green-600" />;
      case 'pending': return <Clock size={16} className="text-yellow-600" />;
      case 'due': return <AlertTriangle size={16} className="text-red-600" />;
      default: return <FileText size={16} className="text-gray-600" />;
    }
  };

  const calculateVAT = () => {
    // Mock VAT calculation
    const baseAmount = 100000; // Example base amount
    const vatRates = [
      { rate: 0.20, amount: baseAmount * 0.20, description: 'TVA 20%' },
      { rate: 0.14, amount: baseAmount * 0.14, description: 'TVA 14%' },
      { rate: 0.10, amount: baseAmount * 0.10, description: 'TVA 10%' },
      { rate: 0.07, amount: baseAmount * 0.07, description: 'TVA 7%' }
    ];
    
    return vatRates;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('nav.taxes')}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Gérez vos obligations fiscales et restez conforme
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
              <Calculator size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              Calculateur TVA
            </button>
            
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
              <Download size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
              Exporter déclarations
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Tax Obligations */}
          <div className="lg:col-span-2 space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">TVA à payer</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">55,000 MAD</p>
                    <p className="text-sm text-red-600">Échéance: 20 Juillet</p>
                  </div>
                  <div className="p-3 bg-red-100 dark:bg-red-900 rounded-full">
                    <AlertTriangle className="text-red-600 dark:text-red-400" size={24} />
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Impôt professionnel</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">8,000 MAD</p>
                    <p className="text-sm text-yellow-600">Échéance: 31 Juillet</p>
                  </div>
                  <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-full">
                    <Clock className="text-yellow-600 dark:text-yellow-400" size={24} />
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Cotisations sociales</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">15,000 MAD</p>
                    <p className="text-sm text-green-600">Payé</p>
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                    <CheckCircle className="text-green-600 dark:text-green-400" size={24} />
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Tax Obligations List */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Obligations fiscales
                </h3>
              </div>
              
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {taxObligations.map((obligation, index) => (
                  <motion.div
                    key={obligation.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(obligation.status)}
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {obligation.type} - {obligation.period}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {obligation.description}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Échéance: {new Date(obligation.dueDate).toLocaleDateString('fr-FR')}
                          </p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {obligation.amount.toLocaleString()} MAD
                        </p>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(obligation.status)}`}>
                          {obligation.status === 'paid' ? 'Payé' : 
                           obligation.status === 'pending' ? 'En attente' : 'À payer'}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Tax Rates Reference */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Taux de TVA au Maroc
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {taxRates.map((rate, index) => (
                  <div key={index} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-gray-900 dark:text-white">{rate.type}</span>
                      <span className="text-lg font-bold text-blue-600 dark:text-blue-400">{rate.rate}</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{rate.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Compliance Calendar */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Calendrier fiscal
              </h3>
              
              <div className="space-y-4">
                {complianceCalendar.map((event, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className={`w-3 h-3 rounded-full mt-2 ${
                        event.status === 'upcoming' ? 'bg-red-500' : 
                        event.status === 'future' ? 'bg-blue-500' : 'bg-gray-400'
                      }`}></div>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">{event.title}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {new Date(event.date).toLocaleDateString('fr-FR', {
                          day: 'numeric',
                          month: 'long',
                          year: 'numeric'
                        })}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* VAT Calculator */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Calculateur TVA
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Montant HT
                  </label>
                  <input
                    type="number"
                    placeholder="0.00"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Taux TVA
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="20">20% - Taux normal</option>
                    <option value="14">14% - Taux réduit</option>
                    <option value="10">10% - Taux réduit</option>
                    <option value="7">7% - Taux réduit</option>
                    <option value="0">0% - Exonéré</option>
                  </select>
                </div>
                
                <button className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors">
                  Calculer
                </button>
                
                <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                  <div className="flex justify-between text-sm">
                    <span>Montant HT:</span>
                    <span>0.00 MAD</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>TVA:</span>
                    <span>0.00 MAD</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t pt-2 mt-2">
                    <span>Total TTC:</span>
                    <span>0.00 MAD</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Taxes;