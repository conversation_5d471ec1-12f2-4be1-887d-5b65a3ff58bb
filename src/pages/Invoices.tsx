import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Send,
  DollarSign,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import Header from "../components/common/Header";
import InvoiceList from "../components/invoices/InvoiceList";
import InvoiceForm from "../components/invoices/InvoiceForm";
import InvoiceView from "../components/invoices/InvoiceView";
import InvoiceFilters from "../components/invoices/InvoiceFilters";
import { useInvoices } from "../hooks/useInvoices";
import { useLanguage } from "../context/LanguageContext";

export type InvoiceStatus = "draft" | "sent" | "paid" | "overdue" | "cancelled";

export interface InvoiceData {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date: string | null;
  customer_id: string | null;
  currency: string;
  subtotal: number;
  tax_total: number;
  total_amount: number;
  paid_amount: number;
  status: InvoiceStatus;
  notes: string | null;
  customers?: {
    name: string;
    email: string;
  };
}

type ViewMode = "list" | "create" | "edit" | "view";

const Invoices: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { invoices, loading, createInvoice, updateInvoice, deleteInvoice } =
    useInvoices();

  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceData | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    dateFrom: "",
    dateTo: "",
    customer: "",
  });

  const handleCreateInvoice = () => {
    setSelectedInvoice(null);
    setViewMode("create");
  };

  const handleEditInvoice = (invoice: InvoiceData) => {
    setSelectedInvoice(invoice);
    setViewMode("edit");
  };

  const handleViewInvoice = (invoice: InvoiceData) => {
    setSelectedInvoice(invoice);
    setViewMode("view");
  };

  const handleDeleteInvoice = async (invoiceId: string) => {
    if (window.confirm(t("invoices.delete.confirm"))) {
      try {
        await deleteInvoice(invoiceId);
      } catch (error) {
        console.error(t("invoices.error.delete"), error);
      }
    }
  };

  const handleSaveInvoice = async (invoiceData: Partial<InvoiceData>) => {
    try {
      if (viewMode === "create") {
        await createInvoice(invoiceData);
      } else if (viewMode === "edit" && selectedInvoice) {
        await updateInvoice(selectedInvoice.id, invoiceData);
      }
      setViewMode("list");
      setSelectedInvoice(null);
    } catch (error) {
      console.error(t("invoices.error.save"), error);
    }
  };

  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customers?.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !filters.status || invoice.status === filters.status;
    const matchesCustomer =
      !filters.customer ||
      invoice.customers?.name
        .toLowerCase()
        .includes(filters.customer.toLowerCase());

    return matchesSearch && matchesStatus && matchesCustomer;
  });

  const getStatusColor = (status: InvoiceStatus) => {
    switch (status) {
      case "draft":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
      case "sent":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "paid":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "overdue":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "cancelled":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  const renderContent = () => {
    switch (viewMode) {
      case "create":
      case "edit":
        return (
          <InvoiceForm
            invoice={selectedInvoice}
            onSave={handleSaveInvoice}
            onCancel={() => setViewMode("list")}
            isEdit={viewMode === "edit"}
          />
        );
      case "view":
        return selectedInvoice ? (
          <InvoiceView
            invoice={selectedInvoice}
            onEdit={() => setViewMode("edit")}
            onClose={() => setViewMode("list")}
          />
        ) : null;
      default:
        return (
          <div className="space-y-6">
            {/* Header Actions */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t("nav.invoices")}
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  {t("invoices.subtitle")}
                </p>
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  <Filter size={16} className={isRTL ? "ml-2" : "mr-2"} />
                  {t("common.filter")}
                </button>

                <button
                  onClick={handleCreateInvoice}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus size={16} className={isRTL ? "ml-2" : "mr-2"} />
                  {t("invoices.create")}
                </button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="space-y-4">
              <div className="relative">
                <Search
                  size={20}
                  className={`absolute ${
                    isRTL ? "right-3" : "left-3"
                  } top-1/2 transform -translate-y-1/2 text-gray-400`}
                />
                <input
                  type="text"
                  placeholder={t("invoices.search.placeholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full ${
                    isRTL ? "pr-10 pl-4" : "pl-10 pr-4"
                  } py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white`}
                />
              </div>

              <AnimatePresence>
                {showFilters && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <InvoiceFilters
                      filters={filters}
                      onFiltersChange={setFilters}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Invoice List */}
            <InvoiceList
              invoices={filteredInvoices}
              loading={loading}
              onView={handleViewInvoice}
              onEdit={handleEditInvoice}
              onDelete={handleDeleteInvoice}
              getStatusColor={getStatusColor}
            />
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={viewMode}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderContent()}
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
};

export default Invoices;
