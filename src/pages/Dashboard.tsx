import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { BarChart2, FileText, PiggyBank, Calculator } from "lucide-react";
import Header from "../components/common/Header";
import StatCard from "../components/dashboard/StatCard";
import CashFlowChart from "../components/dashboard/CashFlowChart";
import InvoicesList from "../components/dashboard/InvoicesList";
import ExpenseList from "../components/dashboard/ExpenseList";
import UpgradeCard from "../components/dashboard/UpgradeCard";
import { CashFlowData, Invoice, Expense } from "../types";
import { useLanguage } from "../context/LanguageContext";
import { useAuth } from "../hooks/useAuth";

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const { isRTL } = useLanguage();

  // Redirect to login if not authenticated, or to onboarding if not completed
  useEffect(() => {
    if (!authLoading && !user) {
      navigate("/auth/login");
    } else if (user && !authLoading) {
      // Check if user has completed onboarding
      const onboardingCompleted = localStorage.getItem("onboarding_completed");
      if (onboardingCompleted !== "true") {
        navigate("/onboarding", { replace: true });
      }
    }
  }, [authLoading, user, navigate]);

  if (!authLoading && !user) {
    return null;
  }

  // Show loading while auth is loading
  if (authLoading) {
    return (
      <div>
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  // Mock data
  const cashFlowData: CashFlowData[] = [
    { date: "Jan", income: 5000, expenses: 3200 },
    { date: "Feb", income: 6000, expenses: 3500 },
    { date: "Mar", income: 8000, expenses: 4200 },
    { date: "Apr", income: 7500, expenses: 4100 },
    { date: "May", income: 9000, expenses: 3800 },
    { date: "Jun", income: 12000, expenses: 4800 },
  ];

  const pendingInvoices: Invoice[] = [
    {
      id: "1",
      number: "INV-001",
      date: "2025-04-15",
      dueDate: "2025-05-15",
      clientName: "Casablanca Trading",
      items: [],
      subtotal: 4500,
      taxTotal: 900,
      total: 5400,
      status: "sent",
    },
    {
      id: "2",
      number: "INV-002",
      date: "2025-04-28",
      dueDate: "2025-05-28",
      clientName: "Rabat Solutions",
      items: [],
      subtotal: 2800,
      taxTotal: 560,
      total: 3360,
      status: "sent",
    },
  ];

  const overdueInvoices: Invoice[] = [
    {
      id: "3",
      number: "INV-003",
      date: "2025-03-10",
      dueDate: "2025-04-10",
      clientName: "Marrakech Ventures",
      items: [],
      subtotal: 7500,
      taxTotal: 1500,
      total: 9000,
      status: "overdue",
    },
  ];

  const recentExpenses: Expense[] = [
    {
      id: "1",
      date: "2025-05-02",
      category: "Office Supplies",
      vendor: "Staples",
      amount: 450,
      taxAmount: 90,
      description: "Office supplies and stationery",
      status: "approved",
    },
    {
      id: "2",
      date: "2025-05-01",
      category: "Utilities",
      vendor: "Maroc Telecom",
      amount: 850,
      taxAmount: 170,
      description: "Internet and phone services",
      status: "approved",
    },
    {
      id: "3",
      date: "2025-04-28",
      category: "Rent",
      vendor: "Immo Invest",
      amount: 5000,
      taxAmount: 0,
      description: "Office rent for May",
      status: "approved",
    },
  ];

  return (
    <div>
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("dashboard.welcome")}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">May 12, 2025</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title={t("dashboard.overview")}
            value="26,450 MAD"
            subtitle="Total revenue this month"
            icon={<BarChart2 size={24} />}
            colorClass="bg-blue-900 dark:bg-blue-800"
          />

          <StatCard
            title={t("dashboard.invoices.pending")}
            value="8,760 MAD"
            subtitle="3 invoices pending"
            icon={<FileText size={24} />}
            colorClass="bg-green-600 dark:bg-green-700"
          />

          <StatCard
            title={t("dashboard.expenses.recent")}
            value="6,300 MAD"
            subtitle="3 expenses this month"
            icon={<PiggyBank size={24} />}
            colorClass="bg-yellow-500 dark:bg-yellow-600"
          />

          <StatCard
            title={t("dashboard.taxes.due")}
            value="5,290 MAD"
            subtitle="Due by Jun 30, 2025"
            icon={<Calculator size={24} />}
            colorClass="bg-red-500 dark:bg-red-600"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <CashFlowChart data={cashFlowData} />
          </div>
          <div>
            <UpgradeCard />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <InvoicesList
            invoices={pendingInvoices}
            title={t("dashboard.invoices.pending")}
          />

          <InvoicesList
            invoices={overdueInvoices}
            title={t("dashboard.invoices.overdue")}
          />

          <ExpenseList expenses={recentExpenses} />
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
