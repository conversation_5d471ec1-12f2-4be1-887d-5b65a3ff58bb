import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  MapPin,
  Building,
  Users,
  Download,
  Upload,
} from "lucide-react";
import Header from "../components/common/Header";
import { useAuth } from "../hooks/useAuth";
import { supabase } from "../lib/supabase";

interface Customer {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  address: any | null;
  tax_id: string | null;
  contact_person: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const Customers: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);

  // Load customers
  useEffect(() => {
    loadCustomers();
  }, [user]);

  const loadCustomers = async () => {
    if (!user?.tenant_id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // For now, since the database might not be properly set up, show empty state
      console.log(t("customers.error.console"));
      setCustomers([]);

      // Uncomment this when database is properly configured:
      /*
      const { data, error } = await supabase
        .from("customers")
        .select("*")
        .eq("tenant_id", user.tenant_id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error loading customers:", error);
        setCustomers([]);
        return;
      }

      // Transform data to match our interface, handling missing fields gracefully
      const transformedData = (data || []).map((customer) => ({
        ...customer,
        address: customer.address || null,
        tax_id: customer.tax_id || null,
        contact_person: customer.contact_person || null,
        is_active: customer.is_active !== undefined ? customer.is_active : true,
      }));

      setCustomers(transformedData);
      */
    } catch (error) {
      console.error(t("customers.error.load"), error);
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter customers based on search term
  const filteredCustomers = customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone?.includes(searchTerm) ||
      customer.contact_person?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateCustomer = () => {
    setSelectedCustomer(null);
    setShowCreateModal(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowEditModal(true);
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowViewModal(true);
  };

  const handleDeleteCustomer = async (customer: Customer) => {
    if (!confirm(t("customers.confirm_delete", { name: customer.name })))
      return;

    try {
      const { error } = await supabase
        .from("customers")
        .delete()
        .eq("id", customer.id);

      if (error) throw error;

      // Reload customers
      loadCustomers();
    } catch (error) {
      console.error(t("customers.error.console"), error);
      alert(t("customers.error.delete"));
    }
  };

  const formatAddress = (address: any) => {
    if (!address) return t("customers.details.no_address");
    if (typeof address === "string") return address;

    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.postal_code) parts.push(address.postal_code);
    if (address.country) parts.push(address.country);

    return parts.join(", ") || "No address";
  };

  // Redirect to login if not authenticated, or to onboarding if not completed
  useEffect(() => {
    if (!authLoading && !user) {
      navigate("/auth/login");
    } else if (user && !authLoading) {
      // Check if user has completed onboarding
      const onboardingCompleted = localStorage.getItem("onboarding_completed");
      if (onboardingCompleted !== "true") {
        navigate("/onboarding", { replace: true });
      }
    }
  }, [authLoading, user, navigate]);

  // Don't render anything if we're about to redirect
  if (!authLoading && !user) {
    return (
      <div>
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show loading while auth is loading
  if (authLoading || loading) {
    return (
      <div>
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("customers.title")}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {t("customers.subtitle")}
            </p>
          </div>

          <div className="flex space-x-3 mt-4 md:mt-0">
            <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
              <Upload className="h-4 w-4 mr-2" />
              {t("customers.import")}
            </button>
            <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
              <Download className="h-4 w-4 mr-2" />
              {t("customers.export")}
            </button>
            <button
              onClick={handleCreateCustomer}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t("customers.add")}
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder={t("customers.search")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <button className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
            <Filter className="h-4 w-4 mr-2" />
            {t("customers.filters")}
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t("customers.total_customers")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {customers.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t("customers.active_customers")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {customers.filter((c) => c.is_active).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Mail className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t("customers.with_email")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {customers.filter((c) => c.email).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Phone className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t("customers.with_phone")}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {customers.filter((c) => c.phone).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t("customers.customer")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t("customers.contact")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t("customers.address")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t("common.filter")}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {t("customers.actions")}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredCustomers.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-12 text-center">
                      <Users className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                        {t("customers.no_customers")}
                      </h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {t("customers.get_started")}
                      </p>
                      <div className="mt-6">
                        <button
                          onClick={handleCreateCustomer}
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          {t("customers.add")}
                        </button>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredCustomers.map((customer) => (
                    <motion.tr
                      key={customer.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {customer.name}
                          </div>
                          {customer.contact_person && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Contact: {customer.contact_person}
                            </div>
                          )}
                          {customer.tax_id && (
                            <div className="text-xs text-gray-400">
                              Tax ID: {customer.tax_id}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          {customer.email && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <Mail className="h-3 w-3 mr-1" />
                              {customer.email}
                            </div>
                          )}
                          {customer.phone && (
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <Phone className="h-3 w-3 mr-1" />
                              {customer.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="truncate max-w-xs">
                            {formatAddress(customer.address)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            customer.is_active
                              ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                              : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                          }`}
                        >
                          {customer.is_active
                            ? t("customers.status.active")
                            : t("customers.status.inactive")}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewCustomer(customer)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditCustomer(customer)}
                            className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteCustomer(customer)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      {/* Create/Edit Customer Modal */}
      {(showCreateModal || showEditModal) && (
        <CustomerFormModal
          customer={selectedCustomer}
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedCustomer(null);
          }}
          onSave={() => {
            loadCustomers();
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedCustomer(null);
          }}
        />
      )}

      {/* View Customer Modal */}
      {showViewModal && selectedCustomer && (
        <CustomerViewModal
          customer={selectedCustomer}
          isOpen={showViewModal}
          onClose={() => {
            setShowViewModal(false);
            setSelectedCustomer(null);
          }}
          onEdit={() => {
            setShowViewModal(false);
            setShowEditModal(true);
          }}
        />
      )}
    </div>
  );
};

// Customer Form Modal Component
interface CustomerFormModalProps {
  customer: Customer | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const CustomerFormModal: React.FC<CustomerFormModalProps> = ({
  customer,
  isOpen,
  onClose,
  onSave,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    contact_person: "",
    tax_id: "",
    street: "",
    city: "",
    postal_code: "",
    country: "Morocco",
    is_active: true,
  });

  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name || "",
        email: customer.email || "",
        phone: customer.phone || "",
        contact_person: customer.contact_person || "",
        tax_id: customer.tax_id || "",
        street: customer.address?.street || "",
        city: customer.address?.city || "",
        postal_code: customer.address?.postal_code || "",
        country: customer.address?.country || "Morocco",
        is_active: customer.is_active,
      });
    } else {
      setFormData({
        name: "",
        email: "",
        phone: "",
        contact_person: "",
        tax_id: "",
        street: "",
        city: "",
        postal_code: "",
        country: "Morocco",
        is_active: true,
      });
    }
  }, [customer]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.tenant_id) return;

    setLoading(true);
    try {
      // Only include fields that exist in the current database schema
      const customerData: any = {
        name: formData.name,
        email: formData.email || null,
        phone: formData.phone || null,
        tenant_id: user.tenant_id,
      };

      // Only add optional fields if they exist in the database
      // These will be added when the database schema is updated
      if (formData.contact_person) {
        customerData.contact_person = formData.contact_person;
      }
      if (formData.tax_id) {
        customerData.tax_id = formData.tax_id;
      }
      if (
        formData.street ||
        formData.city ||
        formData.postal_code ||
        formData.country
      ) {
        customerData.address = {
          street: formData.street,
          city: formData.city,
          postal_code: formData.postal_code,
          country: formData.country,
        };
      }
      customerData.is_active = formData.is_active;

      if (customer) {
        // Update existing customer
        const { error } = await supabase
          .from("customers")
          .update(customerData)
          .eq("id", customer.id);

        if (error) throw error;
      } else {
        // Create new customer
        const { error } = await supabase
          .from("customers")
          .insert([customerData]);

        if (error) throw error;
      }

      onSave();
    } catch (error) {
      console.error(t("customers.error.console"), error);
      alert(t("customers.error.save"));
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
            {customer ? t("customers.edit") : t("customers.add")}
          </h3>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("customers.form.company_name")} *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("customers.form.contact_person")}
                </label>
                <input
                  type="text"
                  value={formData.contact_person}
                  onChange={(e) =>
                    setFormData({ ...formData, contact_person: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("customers.form.email")}
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("customers.form.phone")}
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) =>
                    setFormData({ ...formData, phone: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="+212 6XX XXX XXX"
                />
              </div>
            </div>

            {/* Tax ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {t("customers.form.tax_id")}
              </label>
              <input
                type="text"
                value={formData.tax_id}
                onChange={(e) =>
                  setFormData({ ...formData, tax_id: e.target.value })
                }
                className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Moroccan Tax ID"
              />
            </div>

            {/* Address */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {t("customers.address")}
              </h4>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("customers.form.street")}
                </label>
                <input
                  type="text"
                  value={formData.street}
                  onChange={(e) =>
                    setFormData({ ...formData, street: e.target.value })
                  }
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t("customers.form.city")}
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) =>
                      setFormData({ ...formData, city: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t("customers.form.postal_code")}
                  </label>
                  <input
                    type="text"
                    value={formData.postal_code}
                    onChange={(e) =>
                      setFormData({ ...formData, postal_code: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t("customers.form.country")}
                  </label>
                  <select
                    value={formData.country}
                    onChange={(e) =>
                      setFormData({ ...formData, country: e.target.value })
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="Morocco">{t("country.morocco")}</option>
                    <option value="France">France</option>
                    <option value="Spain">Spain</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) =>
                  setFormData({ ...formData, is_active: e.target.checked })
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="is_active"
                className="ml-2 block text-sm text-gray-900 dark:text-white"
              >
                {t("customers.form.active_customer")}
              </label>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                {t("customers.form.cancel")}
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading
                  ? t("customers.form.saving")
                  : customer
                  ? t("customers.form.update")
                  : t("customers.form.create")}
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

// Customer View Modal Component
interface CustomerViewModalProps {
  customer: Customer;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
}

const CustomerViewModal: React.FC<CustomerViewModalProps> = ({
  customer,
  isOpen,
  onClose,
  onEdit,
}) => {
  if (!isOpen) return null;

  const formatAddress = (address: any) => {
    if (!address) return t("customers.details.no_address");
    if (typeof address === "string") return address;

    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.postal_code) parts.push(address.postal_code);
    if (address.country) parts.push(address.country);

    return parts.join(", ") || "No address provided";
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {t("customers.form.customer_details")}
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={onEdit}
                className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Edit className="h-4 w-4 mr-1" />
                {t("common.edit")}
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>
          </div>

          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                {t("customers.form.basic_info")}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    {t("customers.form.company_name")}
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {customer.name}
                  </p>
                </div>
                {customer.contact_person && (
                  <div>
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                      {t("customers.form.contact_person")}
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {customer.contact_person}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                {t("customers.form.contact_info")}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    {t("customers.form.email")}
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {customer.email || t("customers.details.no_email")}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    {t("customers.form.phone")}
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {customer.phone || t("customers.details.no_phone")}
                  </p>
                </div>
              </div>
            </div>

            {/* Tax Information */}
            {customer.tax_id && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  {t("customers.form.tax_id")}
                </h4>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    {t("customers.form.tax_id")}
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {customer.tax_id}
                  </p>
                </div>
              </div>
            )}

            {/* Address */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                {t("customers.address")}
              </h4>
              <p className="text-sm text-gray-900 dark:text-white">
                {formatAddress(customer.address)}
              </p>
            </div>

            {/* Status */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                {t("customers.form.status")}
              </h4>
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  customer.is_active
                    ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                }`}
              >
                {customer.is_active
                  ? t("customers.status.active")
                  : t("customers.status.inactive")}
              </span>
            </div>

            {/* Timestamps */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Timestamps
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    {t("customers.details.created")}
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {new Date(customer.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-500 dark:text-gray-400">
                    {t("customers.details.updated")}
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {new Date(customer.updated_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Customers;
