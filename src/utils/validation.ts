// Validation utilities for forms across the application

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface FormField {
  value: any;
  rules: ValidationRule;
  label: string;
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^(\+212|0)[5-7]\d{8}$/, // Moroccan phone number format
  taxId: /^[0-9]{8,15}$/, // Moroccan tax ID format
  postalCode: /^[0-9]{5}$/, // Moroccan postal code
  iban: /^MA\d{2}\d{3}\d{21}$/, // Moroccan IBAN format
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/, // Strong password
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  numeric: /^\d+$/,
  decimal: /^\d+(\.\d{1,2})?$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
};

// Validation functions
export const validateField = (value: any, rules: ValidationRule, label: string): string | null => {
  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return `${label} is required`;
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }

  const stringValue = String(value).trim();

  // Minimum length validation
  if (rules.minLength && stringValue.length < rules.minLength) {
    return `${label} must be at least ${rules.minLength} characters long`;
  }

  // Maximum length validation
  if (rules.maxLength && stringValue.length > rules.maxLength) {
    return `${label} must not exceed ${rules.maxLength} characters`;
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    return `${label} format is invalid`;
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return null;
};

export const validateForm = (fields: Record<string, FormField>): ValidationResult => {
  const errors: Record<string, string> = {};
  let isValid = true;

  Object.entries(fields).forEach(([fieldName, field]) => {
    const error = validateField(field.value, field.rules, field.label);
    if (error) {
      errors[fieldName] = error;
      isValid = false;
    }
  });

  return { isValid, errors };
};

// Specific validation functions
export const validateEmail = (email: string): string | null => {
  return validateField(email, { 
    required: true, 
    pattern: VALIDATION_PATTERNS.email 
  }, 'Email');
};

export const validatePassword = (password: string): string | null => {
  return validateField(password, {
    required: true,
    minLength: 8,
    pattern: VALIDATION_PATTERNS.password,
    custom: (value) => {
      if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter';
      if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter';
      if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number';
      return null;
    }
  }, 'Password');
};

export const validatePhone = (phone: string): string | null => {
  return validateField(phone, {
    pattern: VALIDATION_PATTERNS.phone,
    custom: (value) => {
      if (value && !value.startsWith('+212') && !value.startsWith('0')) {
        return 'Phone number must start with +212 or 0';
      }
      return null;
    }
  }, 'Phone number');
};

export const validateTaxId = (taxId: string): string | null => {
  return validateField(taxId, {
    pattern: VALIDATION_PATTERNS.taxId,
    custom: (value) => {
      if (value && (value.length < 8 || value.length > 15)) {
        return 'Tax ID must be between 8 and 15 digits';
      }
      return null;
    }
  }, 'Tax ID');
};

export const validateAmount = (amount: string | number): string | null => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numericAmount)) {
    return 'Amount must be a valid number';
  }
  
  if (numericAmount < 0) {
    return 'Amount cannot be negative';
  }
  
  if (numericAmount > *********.99) {
    return 'Amount is too large';
  }
  
  return null;
};

export const validateDate = (date: string, label: string = 'Date'): string | null => {
  if (!date) return `${label} is required`;
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return `${label} is not a valid date`;
  }
  
  return null;
};

export const validateDateRange = (startDate: string, endDate: string): string | null => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 'Invalid date range';
  }
  
  if (start > end) {
    return 'Start date must be before end date';
  }
  
  return null;
};

export const validateUrl = (url: string): string | null => {
  return validateField(url, {
    pattern: VALIDATION_PATTERNS.url
  }, 'URL');
};

export const validateSlug = (slug: string): string | null => {
  return validateField(slug, {
    pattern: VALIDATION_PATTERNS.slug,
    custom: (value) => {
      if (value && (value.startsWith('-') || value.endsWith('-'))) {
        return 'Slug cannot start or end with a hyphen';
      }
      return null;
    }
  }, 'Slug');
};

// Moroccan-specific validations
export const validateMoroccanPostalCode = (postalCode: string): string | null => {
  return validateField(postalCode, {
    pattern: VALIDATION_PATTERNS.postalCode
  }, 'Postal code');
};

export const validateMoroccanIBAN = (iban: string): string | null => {
  return validateField(iban, {
    pattern: VALIDATION_PATTERNS.iban,
    custom: (value) => {
      if (value && !value.startsWith('MA')) {
        return 'IBAN must be a valid Moroccan IBAN starting with MA';
      }
      return null;
    }
  }, 'IBAN');
};

// Business validation rules
export const validateInvoiceNumber = (invoiceNumber: string): string | null => {
  return validateField(invoiceNumber, {
    required: true,
    minLength: 3,
    maxLength: 20,
    pattern: /^[A-Z0-9-]+$/,
    custom: (value) => {
      if (value && !/^[A-Z]/.test(value)) {
        return 'Invoice number must start with a letter';
      }
      return null;
    }
  }, 'Invoice number');
};

export const validateCompanyName = (companyName: string): string | null => {
  return validateField(companyName, {
    required: true,
    minLength: 2,
    maxLength: 100,
    custom: (value) => {
      if (value && /^\s|\s$/.test(value)) {
        return 'Company name cannot start or end with spaces';
      }
      return null;
    }
  }, 'Company name');
};

export const validatePercentage = (percentage: string | number): string | null => {
  const numericPercentage = typeof percentage === 'string' ? parseFloat(percentage) : percentage;
  
  if (isNaN(numericPercentage)) {
    return 'Percentage must be a valid number';
  }
  
  if (numericPercentage < 0 || numericPercentage > 100) {
    return 'Percentage must be between 0 and 100';
  }
  
  return null;
};

// Form validation hook helper
export const createFormValidator = (schema: Record<string, ValidationRule>) => {
  return (formData: Record<string, any>): ValidationResult => {
    const fields: Record<string, FormField> = {};
    
    Object.entries(schema).forEach(([fieldName, rules]) => {
      fields[fieldName] = {
        value: formData[fieldName],
        rules,
        label: fieldName.charAt(0).toUpperCase() + fieldName.slice(1).replace(/([A-Z])/g, ' $1')
      };
    });
    
    return validateForm(fields);
  };
};

// Error message helpers
export const getFieldError = (errors: Record<string, string>, fieldName: string): string | undefined => {
  return errors[fieldName];
};

export const hasFieldError = (errors: Record<string, string>, fieldName: string): boolean => {
  return !!errors[fieldName];
};

export const getFirstError = (errors: Record<string, string>): string | null => {
  const errorKeys = Object.keys(errors);
  return errorKeys.length > 0 ? errors[errorKeys[0]] : null;
};
