export default {
  translation: {
    // Common
    'app.name': 'MoroccoERP',
    'app.tagline': 'Financial Management for Moroccan Businesses',
    'nav.dashboard': 'Dashboard',
    'nav.invoices': 'Invoices',
    'nav.expenses': 'Expenses',
    'nav.reports': 'Reports',
    'nav.taxes': 'Taxes',
    'nav.settings': 'Settings',
    
    // Common actions
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',
    'common.update': 'Update',
    'common.create': 'Create',
    'common.actions': 'Actions',
    'common.filter': 'Filter',
    'view.all': 'View All',
    
    // Landing page
    'landing.hero.title': 'Financial Management Made Simple',
    'landing.hero.subtitle': 'ERPNext-powered solution for Moroccan SMBs',
    'landing.hero.cta': 'Get Started Free',
    'landing.hero.demo': 'Request Demo',
    
    'landing.features.title': 'Features',
    'landing.features.subtitle': 'Everything your business needs',
    'landing.features.financial.title': 'Financial Management',
    'landing.features.financial.desc': 'Create invoices, track expenses, manage your finances',
    'landing.features.tax.title': 'Moroccan Tax Compliance',
    'landing.features.tax.desc': 'Automatic VAT calculation and tax reports',
    'landing.features.ai.title': 'AI Assistance',
    'landing.features.ai.desc': 'Smart recommendations and automated processes',
    'landing.features.multilingual.title': 'Multi-language Support',
    'landing.features.multilingual.desc': 'Use the app in French, Arabic, or English',
    
    'landing.pricing.title': 'Pricing',
    'landing.pricing.subtitle': 'Choose the plan that works for you',
    'landing.pricing.free.title': 'Free',
    'landing.pricing.free.price': '0 MAD',
    'landing.pricing.free.description': 'For small businesses just getting started',
    'landing.pricing.free.feature1': 'Up to 10 invoices/month',
    'landing.pricing.free.feature2': 'Basic expense tracking',
    'landing.pricing.free.feature3': 'Simple reporting',
    'landing.pricing.free.feature4': 'VAT calculation',
    'landing.pricing.free.feature5': 'Up to 5 users',
    'landing.pricing.free.cta': 'Get Started',
    
    'landing.pricing.premium.title': 'Premium',
    'landing.pricing.premium.price': '399 MAD',
    'landing.pricing.premium.period': '/month',
    'landing.pricing.premium.description': 'For growing businesses with advanced needs',
    'landing.pricing.premium.feature1': 'Unlimited invoices',
    'landing.pricing.premium.feature2': 'Advanced expense tracking',
    'landing.pricing.premium.feature3': 'Comprehensive reporting',
    'landing.pricing.premium.feature4': 'Tax filing assistance',
    'landing.pricing.premium.feature5': 'Multi-currency support',
    'landing.pricing.premium.feature6': 'AI-powered insights',
    'landing.pricing.premium.feature7': 'Priority support',
    'landing.pricing.premium.cta': 'Upgrade Now',
    
    // Dashboard
    'dashboard.welcome': 'Welcome back',
    'dashboard.overview': 'Financial Overview',
    'dashboard.invoices.pending': 'Pending Invoices',
    'dashboard.invoices.overdue': 'Overdue Invoices',
    'dashboard.expenses.recent': 'Recent Expenses',
    'dashboard.cash.flow': 'Cash Flow',
    'dashboard.taxes.due': 'Taxes Due',
    'dashboard.upgrade.title': 'Upgrade Your Plan',
    'dashboard.upgrade.desc': 'Unlock premium features to grow your business',
    'dashboard.upgrade.cta': 'See Plans',
    
    // Invoices
    'invoices.subtitle': 'Manage your invoices and track payments',
    'invoices.create': 'New Invoice',
    'invoices.edit': 'Edit Invoice',
    'invoices.view.title': 'Invoice',
    'invoices.view.invoice': 'INVOICE',
    'invoices.view.bill_to': 'Bill To',
    'invoices.view.paid_amount': 'Paid Amount',
    'invoices.view.balance_due': 'Balance Due',
    'invoices.view.footer_text': 'Thank you for your business!',
    'invoices.view.payment_terms': 'Payment Terms: Net 30 days',
    'invoices.no_customer': 'No Customer',
    'invoices.delete.confirm': 'Are you sure you want to delete this invoice?',
    
    'invoices.search.placeholder': 'Search by number or customer...',
    
    'invoices.empty.title': 'No invoices found',
    'invoices.empty.description': 'Start by creating your first invoice',
    
    'invoices.table.number': 'Number',
    'invoices.table.customer': 'Customer',
    'invoices.table.date': 'Date',
    'invoices.table.due_date': 'Due Date',
    'invoices.table.amount': 'Amount',
    'invoices.table.status': 'Status',
    
    'invoices.status.draft': 'Draft',
    'invoices.status.sent': 'Sent',
    'invoices.status.paid': 'Paid',
    'invoices.status.overdue': 'Overdue',
    'invoices.status.cancelled': 'Cancelled',
    
    'invoices.form.number': 'Invoice Number',
    'invoices.form.date': 'Invoice Date',
    'invoices.form.due_date': 'Due Date',
    'invoices.form.customer': 'Customer',
    'invoices.form.select_customer': 'Select a customer',
    'invoices.form.currency': 'Currency',
    'invoices.form.status': 'Status',
    'invoices.form.items': 'Items',
    'invoices.form.add_item': 'Add Item',
    'invoices.form.description': 'Description',
    'invoices.form.description_placeholder': 'Item or service description',
    'invoices.form.quantity': 'Quantity',
    'invoices.form.unit_price': 'Unit Price',
    'invoices.form.tax_rate': 'Tax Rate',
    'invoices.form.total': 'Total',
    'invoices.form.subtotal': 'Subtotal',
    'invoices.form.tax_total': 'Tax Total',
    'invoices.form.notes': 'Notes',
    'invoices.form.notes_placeholder': 'Additional notes for this invoice...',
    
    'invoices.actions.send': 'Send',
    'invoices.actions.download': 'Download',
    
    'invoices.filters.title': 'Filters',
    'invoices.filters.clear': 'Clear',
    'invoices.filters.status': 'Status',
    'invoices.filters.all_statuses': 'All Statuses',
    'invoices.filters.date_from': 'Date From',
    'invoices.filters.date_to': 'Date To',
    'invoices.filters.customer': 'Customer',
    'invoices.filters.customer_placeholder': 'Customer name...',

    // Expenses
    'expenses.subtitle': 'Manage your expenses and track costs',
    'expenses.create': 'New Expense',
    'expenses.edit': 'Edit Expense',
    'expenses.view.title': 'Expense',
    'expenses.view.details': 'Details',
    'expenses.view.financial': 'Financial Information',
    'expenses.view.receipt': 'Receipt',
    'expenses.view.receipt_attached': 'Receipt attached',
    'expenses.view.download': 'Download',
    'expenses.view.footer_text': 'Expense recorded in the system.',
    'expenses.no_supplier': 'No Supplier',
    'expenses.delete.confirm': 'Are you sure you want to delete this expense?',
    'expenses.upload_receipt': 'Upload Receipt',
    
    'expenses.search.placeholder': 'Search by description or supplier...',
    
    'expenses.empty.title': 'No expenses found',
    'expenses.empty.description': 'Start by creating your first expense',
    
    'expenses.table.description': 'Description',
    'expenses.table.supplier': 'Supplier',
    'expenses.table.category': 'Category',
    'expenses.table.date': 'Date',
    'expenses.table.amount': 'Amount',
    'expenses.table.status': 'Status',
    
    'expenses.status.draft': 'Draft',
    'expenses.status.pending': 'Pending',
    'expenses.status.approved': 'Approved',
    'expenses.status.rejected': 'Rejected',
    'expenses.status.paid': 'Paid',
    
    'expenses.form.date': 'Expense Date',
    'expenses.form.description': 'Description',
    'expenses.form.description_placeholder': 'Expense description...',
    'expenses.form.amount': 'Amount',
    'expenses.form.tax_amount': 'Tax Amount',
    'expenses.form.currency': 'Currency',
    'expenses.form.category': 'Category',
    'expenses.form.select_category': 'Select a category',
    'expenses.form.supplier': 'Supplier',
    'expenses.form.status': 'Status',
    'expenses.form.receipt': 'Receipt',
    'expenses.form.upload_receipt': 'Upload a receipt',
    'expenses.form.or_drag_drop': 'or drag and drop',
    'expenses.form.receipt_uploaded': 'Receipt uploaded',
    'expenses.form.total_amount': 'Total Amount',
    
    'expenses.filters.title': 'Filters',
    'expenses.filters.clear': 'Clear',
    'expenses.filters.status': 'Status',
    'expenses.filters.all_statuses': 'All Statuses',
    'expenses.filters.date_from': 'Date From',
    'expenses.filters.date_to': 'Date To',
    'expenses.filters.category': 'Category',
    'expenses.filters.category_placeholder': 'Category name...',

    // Receipt Upload
    'expenses.upload.title': 'Upload Receipt',
    'expenses.upload.drag_drop': 'Drag and drop your receipt here',
    'expenses.upload.or_click': 'or click to select a file',
    'expenses.upload.choose_file': 'Choose File',
    'expenses.upload.take_photo': 'Take Photo',
    'expenses.upload.supported_formats': 'PNG, JPG, PDF up to 10MB',
    'expenses.upload.processing': 'Processing...',
    'expenses.upload.ai_extracting': 'AI is extracting data from receipt',
    'expenses.upload.success': 'Data extracted successfully!',
    'expenses.upload.data_extracted': 'Review the extracted information below',
    'expenses.upload.extracted_data': 'Extracted Data',
    'expenses.upload.upload_another': 'Upload Another',
    'expenses.upload.create_expense': 'Create Expense',
    'expenses.upload.error': 'Processing Error',
    'expenses.upload.try_again': 'Try Again',
    
    // Language switcher
    'language.french': 'Français',
    'language.arabic': 'العربية',
    'language.english': 'English',
    
    // Auth
    'auth.login': 'Log In',
    'auth.signup': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.forgot': 'Forgot Password?',
    'auth.submit': 'Submit',
    
    // Footer
    'footer.rights': 'All rights reserved.',
  }
};