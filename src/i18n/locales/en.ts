export default {
  translation: {
    // Common
    "app.name": "MoroccoERP",
    "app.tagline": "Financial Management for Moroccan Businesses",
    "nav.dashboard": "Dashboard",
    "nav.invoices": "Invoices",
    "nav.expenses": "Expenses",
    "nav.reports": "Reports",
    "nav.taxes": "Taxes",
    "nav.settings": "Settings",

    // Common actions
    "common.save": "Save",
    "common.cancel": "Cancel",
    "common.edit": "Edit",
    "common.delete": "Delete",
    "common.view": "View",
    "common.update": "Update",
    "common.create": "Create",
    "common.actions": "Actions",
    "common.filter": "Filter",
    "view.all": "View All",

    // Landing page
    "landing.hero.title": "Financial Management Made Simple",
    "landing.hero.subtitle": "ERPNext-powered solution for Moroccan SMBs",
    "landing.hero.cta": "Get Started Free",
    "landing.hero.demo": "Request Demo",

    "landing.features.title": "Features",
    "landing.features.subtitle": "Everything your business needs",
    "landing.features.financial.title": "Financial Management",
    "landing.features.financial.desc":
      "Create invoices, track expenses, manage your finances",
    "landing.features.tax.title": "Moroccan Tax Compliance",
    "landing.features.tax.desc": "Automatic VAT calculation and tax reports",
    "landing.features.ai.title": "AI Assistance",
    "landing.features.ai.desc": "Smart recommendations and automated processes",
    "landing.features.multilingual.title": "Multi-language Support",
    "landing.features.multilingual.desc":
      "Use the app in French, Arabic, or English",

    "landing.pricing.title": "Pricing",
    "landing.pricing.subtitle": "Choose the plan that works for you",
    "landing.pricing.free.title": "Free",
    "landing.pricing.free.price": "0 MAD",
    "landing.pricing.free.description":
      "For small businesses just getting started",
    "landing.pricing.free.feature1": "Up to 10 invoices/month",
    "landing.pricing.free.feature2": "Basic expense tracking",
    "landing.pricing.free.feature3": "Simple reporting",
    "landing.pricing.free.feature4": "VAT calculation",
    "landing.pricing.free.feature5": "Up to 5 users",
    "landing.pricing.free.cta": "Get Started",

    "landing.pricing.premium.title": "Premium",
    "landing.pricing.premium.price": "399 MAD",
    "landing.pricing.premium.period": "/month",
    "landing.pricing.premium.description":
      "For growing businesses with advanced needs",
    "landing.pricing.premium.feature1": "Unlimited invoices",
    "landing.pricing.premium.feature2": "Advanced expense tracking",
    "landing.pricing.premium.feature3": "Comprehensive reporting",
    "landing.pricing.premium.feature4": "Tax filing assistance",
    "landing.pricing.premium.feature5": "Multi-currency support",
    "landing.pricing.premium.feature6": "AI-powered insights",
    "landing.pricing.premium.feature7": "Priority support",
    "landing.pricing.premium.cta": "Upgrade Now",

    // Dashboard
    "dashboard.welcome": "Welcome back",
    "dashboard.overview": "Financial Overview",
    "dashboard.invoices.pending": "Pending Invoices",
    "dashboard.invoices.overdue": "Overdue Invoices",
    "dashboard.expenses.recent": "Recent Expenses",
    "dashboard.cash.flow": "Cash Flow",
    "dashboard.taxes.due": "Taxes Due",
    "dashboard.upgrade.title": "Upgrade Your Plan",
    "dashboard.upgrade.desc": "Unlock premium features to grow your business",
    "dashboard.upgrade.cta": "See Plans",

    // Invoices
    "invoices.subtitle": "Manage your invoices and track payments",
    "invoices.create": "New Invoice",
    "invoices.edit": "Edit Invoice",
    "invoices.view.title": "Invoice",
    "invoices.view.invoice": "INVOICE",
    "invoices.view.bill_to": "Bill To",
    "invoices.view.paid_amount": "Paid Amount",
    "invoices.view.balance_due": "Balance Due",
    "invoices.view.footer_text": "Thank you for your business!",
    "invoices.view.payment_terms": "Payment Terms: Net 30 days",
    "invoices.no_customer": "No Customer",
    "invoices.delete.confirm": "Are you sure you want to delete this invoice?",

    "invoices.search.placeholder": "Search by number or customer...",

    "invoices.empty.title": "No invoices found",
    "invoices.empty.description": "Start by creating your first invoice",

    "invoices.table.number": "Number",
    "invoices.table.customer": "Customer",
    "invoices.table.date": "Date",
    "invoices.table.due_date": "Due Date",
    "invoices.table.amount": "Amount",
    "invoices.table.status": "Status",

    "invoices.status.draft": "Draft",
    "invoices.status.sent": "Sent",
    "invoices.status.paid": "Paid",
    "invoices.status.overdue": "Overdue",
    "invoices.status.cancelled": "Cancelled",

    "invoices.form.number": "Invoice Number",
    "invoices.form.date": "Invoice Date",
    "invoices.form.due_date": "Due Date",
    "invoices.form.customer": "Customer",
    "invoices.form.select_customer": "Select a customer",
    "invoices.form.currency": "Currency",
    "invoices.form.status": "Status",
    "invoices.form.items": "Items",
    "invoices.form.add_item": "Add Item",
    "invoices.form.description": "Description",
    "invoices.form.description_placeholder": "Item or service description",
    "invoices.form.quantity": "Quantity",
    "invoices.form.unit_price": "Unit Price",
    "invoices.form.tax_rate": "Tax Rate",
    "invoices.form.total": "Total",
    "invoices.form.subtotal": "Subtotal",
    "invoices.form.tax_total": "Tax Total",
    "invoices.form.notes": "Notes",
    "invoices.form.notes_placeholder": "Additional notes for this invoice...",

    "invoices.actions.send": "Send",
    "invoices.actions.download": "Download",

    "invoices.filters.title": "Filters",
    "invoices.filters.clear": "Clear",
    "invoices.filters.status": "Status",
    "invoices.filters.all_statuses": "All Statuses",
    "invoices.filters.date_from": "Date From",
    "invoices.filters.date_to": "Date To",
    "invoices.filters.customer": "Customer",
    "invoices.filters.customer_placeholder": "Customer name...",

    // Invoice Errors
    "invoices.error.delete": "Error deleting invoice:",
    "invoices.error.save": "Error saving invoice:",

    // Expenses
    "expenses.subtitle": "Manage your expenses and track costs",
    "expenses.create": "New Expense",
    "expenses.edit": "Edit Expense",
    "expenses.view.title": "Expense",
    "expenses.view.details": "Details",
    "expenses.view.financial": "Financial Information",
    "expenses.view.receipt": "Receipt",
    "expenses.view.receipt_attached": "Receipt attached",
    "expenses.view.download": "Download",
    "expenses.view.footer_text": "Expense recorded in the system.",
    "expenses.no_supplier": "No Supplier",
    "expenses.delete.confirm": "Are you sure you want to delete this expense?",
    "expenses.upload_receipt": "Upload Receipt",

    "expenses.search.placeholder": "Search by description or supplier...",

    "expenses.empty.title": "No expenses found",
    "expenses.empty.description": "Start by creating your first expense",

    "expenses.table.description": "Description",
    "expenses.table.supplier": "Supplier",
    "expenses.table.category": "Category",
    "expenses.table.date": "Date",
    "expenses.table.amount": "Amount",
    "expenses.table.status": "Status",

    "expenses.status.draft": "Draft",
    "expenses.status.pending": "Pending",
    "expenses.status.approved": "Approved",
    "expenses.status.rejected": "Rejected",
    "expenses.status.paid": "Paid",

    "expenses.form.date": "Expense Date",
    "expenses.form.description": "Description",
    "expenses.form.description_placeholder": "Expense description...",
    "expenses.form.amount": "Amount",
    "expenses.form.tax_amount": "Tax Amount",
    "expenses.form.currency": "Currency",
    "expenses.form.category": "Category",
    "expenses.form.select_category": "Select a category",
    "expenses.form.supplier": "Supplier",
    "expenses.form.status": "Status",
    "expenses.form.receipt": "Receipt",
    "expenses.form.upload_receipt": "Upload a receipt",
    "expenses.form.or_drag_drop": "or drag and drop",
    "expenses.form.receipt_uploaded": "Receipt uploaded",
    "expenses.form.total_amount": "Total Amount",

    "expenses.filters.title": "Filters",
    "expenses.filters.clear": "Clear",
    "expenses.filters.status": "Status",
    "expenses.filters.all_statuses": "All Statuses",
    "expenses.filters.date_from": "Date From",
    "expenses.filters.date_to": "Date To",
    "expenses.filters.category": "Category",
    "expenses.filters.category_placeholder": "Category name...",

    // Receipt Upload
    "expenses.upload.title": "Upload Receipt",
    "expenses.upload.drag_drop": "Drag and drop your receipt here",
    "expenses.upload.or_click": "or click to select a file",
    "expenses.upload.choose_file": "Choose File",
    "expenses.upload.take_photo": "Take Photo",
    "expenses.upload.supported_formats": "PNG, JPG, PDF up to 10MB",
    "expenses.upload.processing": "Processing...",
    "expenses.upload.ai_extracting": "AI is extracting data from receipt",
    "expenses.upload.success": "Data extracted successfully!",
    "expenses.upload.data_extracted": "Review the extracted information below",
    "expenses.upload.extracted_data": "Extracted Data",
    "expenses.upload.upload_another": "Upload Another",
    "expenses.upload.create_expense": "Create Expense",
    "expenses.upload.error": "Processing Error",
    "expenses.upload.try_again": "Try Again",

    // Language switcher
    "language.french": "Français",
    "language.arabic": "العربية",
    "language.english": "English",

    // Auth
    "auth.login": "Log In",
    "auth.signup": "Sign Up",
    "auth.email": "Email",
    "auth.password": "Password",
    "auth.forgot": "Forgot Password?",
    "auth.submit": "Submit",

    // Auth Form Fields
    "auth.first_name": "First Name",
    "auth.last_name": "Last Name",
    "auth.company_name": "Company Name",
    "auth.confirm_password": "Confirm Password",
    "auth.show_password": "Show password",
    "auth.hide_password": "Hide password",

    // Auth Form Placeholders
    "auth.placeholder.email": "<EMAIL>",
    "auth.placeholder.password": "••••••••",
    "auth.placeholder.company": "Your company name",

    // Auth Validation Messages
    "auth.error.required_fields": "Please fill in all required fields",
    "auth.error.invalid_email": "Please enter a valid email address",
    "auth.error.password_length": "Password must be at least 6 characters long",
    "auth.error.confirm_password": "Please confirm your password",
    "auth.error.passwords_mismatch": "Passwords do not match",
    "auth.error.name_required": "Please enter your first and last name",
    "auth.error.company_required": "Please enter your company name",
    "auth.error.unexpected": "An unexpected error occurred. Please try again.",

    // Auth Loading States
    "auth.loading.signin": "Signing in...",
    "auth.loading.signup": "Creating account...",

    // Auth Actions
    "auth.demo_mode": "🚀 Try Demo Mode",
    "auth.new_to_app": "New to MoroccoERP?",
    "auth.already_account": "Already have an account?",
    "auth.demo_email": "<EMAIL>",

    // Footer
    "footer.rights": "All rights reserved.",

    // Onboarding
    "onboarding.welcome": "Welcome to MoroccanERP",
    "onboarding.tagline":
      "Let's get you started with the perfect setup for your business",
    "onboarding.status.question": "What's your current business situation?",
    "onboarding.status.description":
      "Choose the option that best describes your situation to get personalized guidance",
    "onboarding.existing.title": "I already have a company",
    "onboarding.existing.description":
      "My business is already registered in Morocco with ICE number, RC, and other legal documents",
    "onboarding.existing.quick_setup": "Quick setup (5 minutes)",
    "onboarding.existing.immediate_access": "Immediate access to all features",
    "onboarding.existing.auto_compliance": "Automatic tax compliance setup",
    "onboarding.new.title": "I need to create a company",
    "onboarding.new.description":
      "I want to start a business in Morocco and need guidance on company formation and legal requirements",
    "onboarding.new.complete_guide": "Complete business formation guide",
    "onboarding.new.expert_partnership": "Expert accountant partnership",
    "onboarding.new.tax_explained": "Tax implications explained",
    "onboarding.continue": "Continue",
    "onboarding.help_text":
      "Don't worry, you can always change this later in your settings",

    // Company Formation Guide
    "formation.choose_structure": "Choose Your Business Structure",
    "formation.understanding_types":
      "Understanding the different business types available in Morocco",
    "formation.auto_entrepreneur": "Auto-Entrepreneur",
    "formation.auto_entrepreneur_ar": "المقاول الذاتي",
    "formation.sarl": "SARL (Limited Liability Company)",
    "formation.sarl_ar": "شركة ذات المسؤولية المحدودة",
    "formation.sa": "SA (Public Limited Company)",
    "formation.sa_ar": "شركة مساهمة",
    "formation.simple": "Simple",
    "formation.medium": "Medium",
    "formation.complex": "Complex",
    "formation.min_capital": "Min Capital",
    "formation.partners": "Partners",
    "formation.tax_rate": "Tax Rate",
    "formation.formation_time": "Formation Time",
    "formation.best_for": "Best for",
    "formation.back": "Back",
    "formation.next": "Next",
    "formation.complete": "Complete",

    // Formation Business Types
    "formation.auto_entrepreneur_desc":
      "Perfect for freelancers and small service providers",
    "formation.sarl_desc": "Most popular choice for small to medium businesses",
    "formation.sa_desc": "For larger businesses planning to raise capital",

    // Formation Steps
    "formation.steps.overview": "Business Types Overview",
    "formation.steps.tax": "Tax Implications",
    "formation.steps.timeline": "Timeline & Process",
    "formation.steps.choice": "Make Your Choice",

    // Tax Implications
    "formation.tax.title": "Tax Implications for {businessType}",
    "formation.tax.subtitle":
      "Understanding your tax obligations and deadlines",
    "formation.tax.breakdown": "Tax Breakdown for {businessType}",
    "formation.tax.monthly_declarations": "Monthly Declarations",
    "formation.tax.annual_limits": "Annual Limits",
    "formation.tax.benefits": "Benefits",
    "formation.tax.corporate_income": "Corporate Income Tax",
    "formation.tax.vat_registration": "VAT Registration",
    "formation.tax.key_deadlines": "Key Deadlines",

    // Timeline & Process
    "formation.timeline.title": "Formation Timeline & Process",
    "formation.timeline.subtitle":
      "Step-by-step process to register your business in Morocco",
    "formation.timeline.pro_tip": "Pro Tip: Let Us Handle This For You",
    "formation.timeline.pro_desc":
      "Our partner accountants can handle the entire process for you, ensuring compliance and saving you time. Typical cost: 3,000-8,000 MAD depending on business type.",

    // Final Choice
    "formation.choice.title": "Ready to Start Your Business?",
    "formation.choice.subtitle":
      "Choose how you'd like to proceed with your company formation",
    "formation.choice.yes_title": "Yes, Help Me Form My Company",
    "formation.choice.yes_desc":
      "Connect with our certified partner accountants for professional company formation",
    "formation.choice.no_title": "I'll Handle It Myself",
    "formation.choice.no_desc":
      "Proceed with the information provided and handle formation independently",
    "formation.choice.expert_guidance": "Expert guidance throughout",
    "formation.choice.paperwork_handled": "All paperwork handled",
    "formation.choice.compliance_guaranteed": "Compliance guaranteed",
    "formation.choice.complete_guide": "Complete guide provided",
    "formation.choice.start_immediately": "Start using ERP immediately",
    "formation.choice.support_available": "Support available anytime",

    // Business Type Details
    "formation.liability": "Liability",
    "formation.auto_entrepreneur_liability": "Unlimited personal liability",
    "formation.sarl_liability": "Limited to capital contribution",
    "formation.sa_liability": "Limited to share value",
    "formation.auto_entrepreneur_best_for":
      "Freelancers, consultants, small service providers",
    "formation.sarl_best_for":
      "Small to medium businesses, partnerships, growing companies",
    "formation.sa_best_for":
      "Large businesses, companies seeking investment, public companies",

    // Timeline Steps
    "formation.timeline.step1_title": "Certificate of Negative",
    "formation.timeline.step1_desc": "Reserve your company name at OMPIC",
    "formation.timeline.step2_title": "Bank Account Opening",
    "formation.timeline.step2_desc":
      "Open corporate bank account and deposit capital",
    "formation.timeline.step3_title": "Notary Registration",
    "formation.timeline.step3_desc": "Sign articles of association with notary",
    "formation.timeline.step4_title": "Commercial Register",
    "formation.timeline.step4_desc": "Register with Commercial Court",
    "formation.timeline.step5_title": "Tax Registration",
    "formation.timeline.step5_desc": "Register for taxes and get ICE number",
    "formation.timeline.step6_title": "CNSS Registration",
    "formation.timeline.step6_desc":
      "Register for social security (if employees)",

    // Professional Formation Services
    "partnership.title": "Connect with Formation Experts",
    "partnership.subtitle":
      "Get professional help forming your {businessType} in Morocco",
    "partnership.why_choose": "Why Choose Our Formation Services?",
    "partnership.certified": "Certified Professionals",
    "partnership.certified_desc":
      "Our team includes certified accountants and legal experts with 5+ years experience",
    "partnership.fast_processing": "Fast Processing",
    "partnership.fast_desc":
      "Average formation time: 2-3 weeks vs 6-8 weeks DIY",
    "partnership.guaranteed": "Guaranteed Compliance",
    "partnership.guaranteed_desc":
      "100% compliance guarantee with Moroccan regulations",
    "partnership.ongoing_support": "Ongoing Support",
    "partnership.ongoing_desc":
      "Continued accounting and tax support after formation",
    "partnership.get_consultation": "Get Your Free Consultation",
    "partnership.consultation_desc":
      "Fill out this form and we'll connect you with a formation expert within 24 hours",
    "partnership.first_name": "First Name",
    "partnership.last_name": "Last Name",
    "partnership.email": "Email Address",
    "partnership.phone": "Phone Number",
    "partnership.city": "City",
    "partnership.timeline": "Preferred Timeline",
    "partnership.budget": "Budget Range (Optional)",
    "partnership.additional_info": "Additional Information (Optional)",
    "partnership.submit": "Get Free Consultation",
    "partnership.skip": "Skip for Now",
    "partnership.secure": "Secure & Confidential",
    "partnership.response_time": "24h Response Time",
    "partnership.no_obligation": "No Obligation",

    // Partnership Form Options
    "partnership.timeline_asap": "As soon as possible",
    "partnership.timeline_asap_desc": "Within 1-2 weeks",
    "partnership.timeline_1month": "Within 1 month",
    "partnership.timeline_1month_desc": "No rush, but soon",
    "partnership.timeline_3months": "Within 3 months",
    "partnership.timeline_3months_desc": "Planning ahead",
    "partnership.timeline_flexible": "Flexible timeline",
    "partnership.timeline_flexible_desc": "Just exploring options",
    "partnership.budget_basic": "Basic formation",
    "partnership.budget_standard": "Standard service",
    "partnership.budget_premium": "Premium package",
    "partnership.budget_custom": "Custom quote",
    "partnership.budget_custom_desc": "Complex requirements",
    "partnership.city_placeholder": "Select your city",
    "partnership.placeholder_first_name": "Your first name",
    "partnership.placeholder_last_name": "Your last name",
    "partnership.placeholder_email": "<EMAIL>",
    "partnership.placeholder_phone": "+212 6XX XXX XXX",
    "partnership.placeholder_additional":
      "Tell us about your business plans, specific requirements, or any questions you have...",

    // Success Messages
    "success.welcome_title": "Welcome to MoroccanERP!",
    "success.existing_company":
      "Your company information has been successfully configured. You're ready to start managing your business with our comprehensive ERP system.",
    "success.formation_requested":
      "Thank you for your interest in our company formation services! Our partner accountant will contact you within 24 hours.",
    "success.diy_formation":
      "You've chosen to handle company formation independently. We've provided you with comprehensive guidance to help you through the process.",
    "success.start_using": "Start Using MoroccanERP",
    "success.update_later":
      "You can always update your company information later in settings",

    // Success Step Details
    "success.whats_next": "What's Next?",
    "success.tax_configured":
      "Your tax compliance settings have been automatically configured",
    "success.add_customers":
      "Start by adding your first customers and suppliers",
    "success.create_invoice": "Create your first invoice to test the system",
    "success.explore_dashboard": "Explore the dashboard for business insights",
    "success.what_happens_next": "What Happens Next?",
    "success.expert_call": "A formation expert will call you within 24 hours",
    "success.free_consultation":
      "Free consultation to discuss your specific needs",
    "success.transparent_pricing": "Transparent pricing and timeline provided",
    "success.professional_process":
      "Complete formation process handled professionally",
    "success.erp_preconfigured":
      "Your ERP system will be pre-configured for your business",
    "success.meanwhile": "Meanwhile, You Can:",
    "success.explore_demo": "Explore the ERP system with demo data",
    "success.learn_compliance": "Learn about Moroccan tax compliance features",
    "success.setup_processes": "Set up your business processes and workflows",
    "success.prepare_lists": "Prepare your customer and supplier lists",
    "success.formation_checklist": "Your Formation Checklist:",
    "success.certificate_negative": "Certificate of Negative (OMPIC)",
    "success.bank_account": "Bank account opening and capital deposit",
    "success.notary_registration":
      "Notary registration and articles of association",
    "success.commercial_register": "Commercial register inscription",
    "success.tax_registration": "Tax registration and ICE number",
    "success.cnss_registration": "CNSS registration (if applicable)",
    "success.need_help_later": "Need Help Later?",
    "success.help_available":
      "Our formation experts are always available if you change your mind. You can request assistance anytime from your settings page.",

    // Customers
    "customers.title": "Customers",
    "customers.subtitle": "Manage your customer database and relationships",
    "customers.add": "Add Customer",
    "customers.edit": "Edit Customer",
    "customers.view": "View Customer",
    "customers.delete": "Delete Customer",
    "customers.search": "Search customers...",
    "customers.no_customers": "No customers found",
    "customers.loading": "Loading customers...",

    // Customer Form
    "customers.form.basic_info": "Basic Information",
    "customers.form.contact_info": "Contact Information",
    "customers.form.address_info": "Address Information",
    "customers.form.name": "Customer Name",
    "customers.form.email": "Email Address",
    "customers.form.phone": "Phone Number",
    "customers.form.contact_person": "Contact Person",
    "customers.form.tax_id": "Tax ID",
    "customers.form.street": "Street Address",
    "customers.form.city": "City",
    "customers.form.postal_code": "Postal Code",
    "customers.form.country": "Country",
    "customers.form.status": "Status",
    "customers.form.active": "Active",
    "customers.form.inactive": "Inactive",
    "customers.form.save": "Save Customer",
    "customers.form.cancel": "Cancel",
    "customers.form.create": "Create Customer",
    "customers.form.update": "Update Customer",
    "customers.form.saving": "Saving...",

    // Customer Status
    "customers.status.active": "Active",
    "customers.status.inactive": "Inactive",

    // Customer Actions
    "customers.actions.edit": "Edit",
    "customers.actions.view": "View",
    "customers.actions.delete": "Delete",
    "customers.confirm_delete": "Are you sure you want to delete {name}?",

    // Customer Details
    "customers.details.no_address": "No address provided",
    "customers.details.no_email": "Not provided",
    "customers.details.no_phone": "Not provided",
    "customers.details.created": "Created",
    "customers.details.updated": "Updated",

    // Customer Errors
    "customers.error.load": "Error loading customers",
    "customers.error.delete": "Failed to delete customer",
    "customers.error.save": "Failed to save customer",
    "customers.error.console": "Error saving customer:",

    // Suppliers
    "suppliers.title": "Suppliers",
    "suppliers.subtitle": "Manage your supplier database and relationships",
    "suppliers.add": "Add Supplier",
    "suppliers.edit": "Edit Supplier",
    "suppliers.view": "View Supplier",
    "suppliers.delete": "Delete Supplier",
    "suppliers.search": "Search suppliers...",
    "suppliers.no_suppliers": "No suppliers found",
    "suppliers.loading": "Loading suppliers...",

    // Supplier Categories
    "suppliers.category.raw_materials": "Raw Materials",
    "suppliers.category.professional_services": "Professional Services",
    "suppliers.category.industrial_equipment": "Industrial Equipment",
    "suppliers.category.office_supplies": "Office Supplies",
    "suppliers.category.technology_it": "Technology & IT",
    "suppliers.category.utilities": "Utilities",
    "suppliers.category.transport_logistics": "Transportation & Logistics",
    "suppliers.category.construction": "Construction & Public Works",
    "suppliers.category.textile_leather": "Textile & Leather",
    "suppliers.category.agri_food": "Agri-food",
    "suppliers.category.pharmaceutical": "Pharmaceutical",
    "suppliers.category.automotive": "Automotive",
    "suppliers.category.renewable_energy": "Renewable Energy",
    "suppliers.category.financial_services": "Financial Services",
    "suppliers.category.other": "Other",

    // Payment Terms
    "suppliers.payment.cash": "Cash",
    "suppliers.payment.net_15": "Net 15 days",
    "suppliers.payment.net_30": "Net 30 days",
    "suppliers.payment.net_60": "Net 60 days",
    "suppliers.payment.net_90": "Net 90 days",
    "suppliers.payment.end_month": "End of month",
    "suppliers.payment.cod": "Cash on delivery",
    "suppliers.payment.prepayment": "Prepayment required",
    "suppliers.payment.documentary_credit": "Documentary credit",
    "suppliers.payment.bank_transfer": "Bank transfer",

    // Supplier Form
    "suppliers.form.basic_info": "Basic Information",
    "suppliers.form.contact_info": "Contact Information",
    "suppliers.form.business_info": "Business Information",
    "suppliers.form.address_info": "Address Information",
    "suppliers.form.name": "Supplier Name",
    "suppliers.form.email": "Email Address",
    "suppliers.form.phone": "Phone Number",
    "suppliers.form.contact_person": "Contact Person",
    "suppliers.form.tax_id": "Tax ID",
    "suppliers.form.ice_number": "ICE Number",
    "suppliers.form.rc_number": "RC Number",
    "suppliers.form.category": "Category",
    "suppliers.form.payment_terms": "Payment Terms",
    "suppliers.form.rating": "Rating",
    "suppliers.form.street": "Street Address",
    "suppliers.form.city": "City",
    "suppliers.form.postal_code": "Postal Code",
    "suppliers.form.country": "Country",
    "suppliers.form.status": "Status",
    "suppliers.form.active": "Active",
    "suppliers.form.inactive": "Inactive",
    "suppliers.form.save": "Save Supplier",
    "suppliers.form.cancel": "Cancel",
    "suppliers.form.create": "Create Supplier",
    "suppliers.form.update": "Update Supplier",
    "suppliers.form.saving": "Saving...",

    // Supplier Status
    "suppliers.status.active": "Active",
    "suppliers.status.inactive": "Inactive",

    // Supplier Actions
    "suppliers.actions.edit": "Edit",
    "suppliers.actions.view": "View",
    "suppliers.actions.delete": "Delete",
    "suppliers.confirm_delete": "Are you sure you want to delete {name}?",

    // Supplier Details
    "suppliers.details.no_address": "No address provided",
    "suppliers.details.no_email": "Not provided",
    "suppliers.details.no_phone": "Not provided",
    "suppliers.details.no_tax_id": "Not provided",
    "suppliers.details.no_ice": "Not provided",
    "suppliers.details.no_rc": "Not provided",
    "suppliers.details.created": "Created",
    "suppliers.details.updated": "Updated",

    // Supplier Errors
    "suppliers.error.load": "Error loading suppliers",
    "suppliers.error.delete": "Failed to delete supplier",
    "suppliers.error.save": "Failed to save supplier",
    "suppliers.error.console": "Error saving supplier:",
    "suppliers.error.table_not_created":
      "Suppliers table not yet created - showing empty state",

    // Countries
    "country.morocco": "Morocco",
  },
};
