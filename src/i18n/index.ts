import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from './locales/en';
import fr from './locales/fr';
import ar from './locales/ar';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en,
      fr,
      ar
    },
    lng: 'fr', // Default language for Morocco
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false // React already safes from XSS
    }
  });

export default i18n;