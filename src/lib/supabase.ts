import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types
export interface Database {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: string;
          name: string;
          slug: string;
          domain: string | null;
          logo_url: string | null;
          address: any | null;
          phone: string | null;
          email: string | null;
          tax_id: string | null;
          registration_number: string | null;
          default_currency: string;
          default_language: string;
          timezone: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          domain?: string | null;
          logo_url?: string | null;
          address?: any | null;
          phone?: string | null;
          email?: string | null;
          tax_id?: string | null;
          registration_number?: string | null;
          default_currency?: string;
          default_language?: string;
          timezone?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          domain?: string | null;
          logo_url?: string | null;
          address?: any | null;
          phone?: string | null;
          email?: string | null;
          tax_id?: string | null;
          registration_number?: string | null;
          default_currency?: string;
          default_language?: string;
          timezone?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          tenant_id: string;
          email: string;
          first_name: string | null;
          last_name: string | null;
          avatar_url: string | null;
          phone: string | null;
          language: string;
          timezone: string;
          is_active: boolean;
          last_login_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          tenant_id: string;
          email: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          language?: string;
          timezone?: string;
          is_active?: boolean;
          last_login_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          email?: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          phone?: string | null;
          language?: string;
          timezone?: string;
          is_active?: boolean;
          last_login_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      invoices: {
        Row: {
          id: string;
          tenant_id: string;
          company_id: string;
          customer_id: string | null;
          invoice_number: string;
          invoice_date: string;
          due_date: string | null;
          currency: string;
          exchange_rate: number;
          subtotal: number;
          tax_total: number;
          discount_total: number;
          total_amount: number;
          paid_amount: number;
          status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
          notes: string | null;
          terms_conditions: string | null;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          company_id: string;
          customer_id?: string | null;
          invoice_number: string;
          invoice_date?: string;
          due_date?: string | null;
          currency?: string;
          exchange_rate?: number;
          subtotal?: number;
          tax_total?: number;
          discount_total?: number;
          total_amount?: number;
          paid_amount?: number;
          status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
          notes?: string | null;
          terms_conditions?: string | null;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          company_id?: string;
          customer_id?: string | null;
          invoice_number?: string;
          invoice_date?: string;
          due_date?: string | null;
          currency?: string;
          exchange_rate?: number;
          subtotal?: number;
          tax_total?: number;
          discount_total?: number;
          total_amount?: number;
          paid_amount?: number;
          status?: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
          notes?: string | null;
          terms_conditions?: string | null;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      expenses: {
        Row: {
          id: string;
          tenant_id: string;
          company_id: string;
          supplier_id: string | null;
          expense_number: string | null;
          expense_date: string;
          category_id: string | null;
          description: string;
          amount: number;
          tax_amount: number;
          currency: string;
          exchange_rate: number;
          receipt_url: string | null;
          status: 'draft' | 'pending' | 'approved' | 'rejected' | 'paid';
          approved_by: string | null;
          approved_at: string | null;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          tenant_id: string;
          company_id: string;
          supplier_id?: string | null;
          expense_number?: string | null;
          expense_date?: string;
          category_id?: string | null;
          description: string;
          amount: number;
          tax_amount?: number;
          currency?: string;
          exchange_rate?: number;
          receipt_url?: string | null;
          status?: 'draft' | 'pending' | 'approved' | 'rejected' | 'paid';
          approved_by?: string | null;
          approved_at?: string | null;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          tenant_id?: string;
          company_id?: string;
          supplier_id?: string | null;
          expense_number?: string | null;
          expense_date?: string;
          category_id?: string | null;
          description?: string;
          amount?: number;
          tax_amount?: number;
          currency?: string;
          exchange_rate?: number;
          receipt_url?: string | null;
          status?: 'draft' | 'pending' | 'approved' | 'rejected' | 'paid';
          approved_by?: string | null;
          approved_at?: string | null;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_user_tenant_id: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      user_has_role: {
        Args: {
          required_role: 'super_admin' | 'tenant_admin' | 'accountant' | 'manager' | 'employee' | 'viewer';
        };
        Returns: boolean;
      };
    };
    Enums: {
      user_role_type: 'super_admin' | 'tenant_admin' | 'accountant' | 'manager' | 'employee' | 'viewer';
      subscription_status: 'active' | 'cancelled' | 'expired' | 'trial';
      invoice_status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
      expense_status: 'draft' | 'pending' | 'approved' | 'rejected' | 'paid';
      payment_status: 'pending' | 'completed' | 'failed' | 'cancelled';
      notification_type: 'info' | 'warning' | 'error' | 'success';
    };
  };
}