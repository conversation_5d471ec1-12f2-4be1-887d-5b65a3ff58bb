/*
  # Default Data and Sample Records

  1. Default Tax Rates for Morocco
  2. Default Chart of Accounts
  3. Default Expense Categories
  4. Sample Settings
  5. Default Notifications

  Features:
  - Moroccan VAT rates
  - Standard accounting structure
  - Common expense categories
  - System configuration
*/

-- Function to create default data for a new tenant
CREATE OR REPLACE FUNCTION create_tenant_defaults(tenant_uuid uuid)
RETURNS void AS $$
BEGIN
  -- Insert default Moroccan tax rates
  INSERT INTO tax_rates (tenant_id, name, rate, type, description) VALUES
  (tenant_uuid, 'TVA 0%', 0.00, 'VAT', 'Exonéré de TVA'),
  (tenant_uuid, 'TVA 7%', 7.00, 'VAT', 'Taux réduit - Produits de première nécessité'),
  (tenant_uuid, 'TVA 10%', 10.00, 'VAT', 'Taux réduit - Services touristiques'),
  (tenant_uuid, 'TVA 14%', 14.00, 'VAT', 'Taux réduit - Services financiers'),
  (tenant_uuid, 'TVA 20%', 20.00, 'VAT', 'Taux normal - Biens et services');

  -- Insert default expense categories
  INSERT INTO expense_categories (tenant_id, name, description) VALUES
  (tenant_uuid, 'Fournitures de bureau', 'Papeterie, matériel de bureau'),
  (tenant_uuid, 'Télécommunications', 'Internet, téléphone, mobile'),
  (tenant_uuid, 'Loyer', 'Loyer des locaux commerciaux'),
  (tenant_uuid, 'Électricité', 'Factures d''électricité'),
  (tenant_uuid, 'Eau', 'Factures d''eau'),
  (tenant_uuid, 'Carburant', 'Essence, diesel pour véhicules'),
  (tenant_uuid, 'Maintenance', 'Réparations et maintenance'),
  (tenant_uuid, 'Assurance', 'Primes d''assurance'),
  (tenant_uuid, 'Formation', 'Formation du personnel'),
  (tenant_uuid, 'Marketing', 'Publicité et marketing'),
  (tenant_uuid, 'Frais bancaires', 'Commissions et frais bancaires'),
  (tenant_uuid, 'Déplacements', 'Frais de déplacement et transport'),
  (tenant_uuid, 'Repas d''affaires', 'Restaurants et réceptions'),
  (tenant_uuid, 'Honoraires', 'Consultants et services professionnels'),
  (tenant_uuid, 'Impôts et taxes', 'Taxes locales et impôts');

  -- Insert default settings
  INSERT INTO settings (tenant_id, key, value, description) VALUES
  (tenant_uuid, 'company_name', '""', 'Nom de l''entreprise'),
  (tenant_uuid, 'default_currency', '"MAD"', 'Devise par défaut'),
  (tenant_uuid, 'default_language', '"fr"', 'Langue par défaut'),
  (tenant_uuid, 'fiscal_year_start', '1', 'Mois de début d''exercice fiscal (1-12)'),
  (tenant_uuid, 'invoice_prefix', '"FAC-"', 'Préfixe des numéros de facture'),
  (tenant_uuid, 'expense_prefix', '"DEP-"', 'Préfixe des numéros de dépense'),
  (tenant_uuid, 'default_payment_terms', '30', 'Délai de paiement par défaut (jours)'),
  (tenant_uuid, 'vat_number', '""', 'Numéro de TVA de l''entreprise'),
  (tenant_uuid, 'registration_number', '""', 'Numéro d''immatriculation'),
  (tenant_uuid, 'email_notifications', 'true', 'Activer les notifications par email'),
  (tenant_uuid, 'auto_backup', 'true', 'Sauvegarde automatique des données'),
  (tenant_uuid, 'invoice_template', '"standard"', 'Modèle de facture par défaut');

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a complete tenant setup
CREATE OR REPLACE FUNCTION setup_new_tenant(
  tenant_name text,
  tenant_slug text,
  admin_email text,
  admin_first_name text DEFAULT '',
  admin_last_name text DEFAULT '',
  plan_slug text DEFAULT 'free'
)
RETURNS uuid AS $$
DECLARE
  new_tenant_id uuid;
  plan_id uuid;
  admin_user_id uuid;
BEGIN
  -- Create tenant
  INSERT INTO tenants (name, slug, email)
  VALUES (tenant_name, tenant_slug, admin_email)
  RETURNING id INTO new_tenant_id;

  -- Get billing plan
  SELECT id INTO plan_id FROM billing_plans WHERE slug = plan_slug;

  -- Create subscription
  INSERT INTO tenant_subscriptions (tenant_id, billing_plan_id, status)
  VALUES (new_tenant_id, plan_id, 'trial');

  -- Create default company
  INSERT INTO companies (tenant_id, name, is_default, is_active)
  VALUES (new_tenant_id, tenant_name, true, true);

  -- Create default data for tenant
  PERFORM create_tenant_defaults(new_tenant_id);

  RETURN new_tenant_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to setup user after signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
DECLARE
  tenant_id_var uuid;
BEGIN
  -- Check if user already exists in users table
  IF EXISTS (SELECT 1 FROM users WHERE id = NEW.id) THEN
    RETURN NEW;
  END IF;

  -- For now, create a default tenant for each new user
  -- In production, you'd handle tenant assignment differently
  SELECT setup_new_tenant(
    COALESCE(NEW.raw_user_meta_data->>'company_name', 'Ma Société'),
    LOWER(REPLACE(COALESCE(NEW.raw_user_meta_data->>'company_name', 'ma-societe-' || substr(NEW.id::text, 1, 8)), ' ', '-')),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    'free'
  ) INTO tenant_id_var;

  -- Insert user record
  INSERT INTO users (
    id,
    tenant_id,
    email,
    first_name,
    last_name
  ) VALUES (
    NEW.id,
    tenant_id_var,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', '')
  );

  -- Assign tenant admin role
  INSERT INTO user_roles (user_id, tenant_id, role)
  VALUES (NEW.id, tenant_id_var, 'tenant_admin');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to create audit log entries
CREATE OR REPLACE FUNCTION create_audit_log()
RETURNS trigger AS $$
DECLARE
  tenant_id_var uuid;
  user_id_var uuid;
BEGIN
  -- Get tenant_id from the record
  IF TG_OP = 'DELETE' THEN
    tenant_id_var := OLD.tenant_id;
  ELSE
    tenant_id_var := NEW.tenant_id;
  END IF;

  -- Get current user
  user_id_var := auth.uid();

  -- Skip audit for audit_logs table itself
  IF TG_TABLE_NAME = 'audit_logs' THEN
    IF TG_OP = 'DELETE' THEN
      RETURN OLD;
    ELSE
      RETURN NEW;
    END IF;
  END IF;

  -- Create audit log entry
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_logs (tenant_id, user_id, table_name, record_id, action, new_values)
    VALUES (tenant_id_var, user_id_var, TG_TABLE_NAME, NEW.id, 'INSERT', to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_logs (tenant_id, user_id, table_name, record_id, action, old_values, new_values)
    VALUES (tenant_id_var, user_id_var, TG_TABLE_NAME, NEW.id, 'UPDATE', to_jsonb(OLD), to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_logs (tenant_id, user_id, table_name, record_id, action, old_values)
    VALUES (tenant_id_var, user_id_var, TG_TABLE_NAME, OLD.id, 'DELETE', to_jsonb(OLD));
    RETURN OLD;
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit triggers for important tables
CREATE TRIGGER audit_invoices AFTER INSERT OR UPDATE OR DELETE ON invoices FOR EACH ROW EXECUTE FUNCTION create_audit_log();
CREATE TRIGGER audit_expenses AFTER INSERT OR UPDATE OR DELETE ON expenses FOR EACH ROW EXECUTE FUNCTION create_audit_log();
CREATE TRIGGER audit_payments AFTER INSERT OR UPDATE OR DELETE ON payments FOR EACH ROW EXECUTE FUNCTION create_audit_log();
CREATE TRIGGER audit_customers AFTER INSERT OR UPDATE OR DELETE ON customers FOR EACH ROW EXECUTE FUNCTION create_audit_log();
CREATE TRIGGER audit_suppliers AFTER INSERT OR UPDATE OR DELETE ON suppliers FOR EACH ROW EXECUTE FUNCTION create_audit_log();
CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users FOR EACH ROW EXECUTE FUNCTION create_audit_log();
CREATE TRIGGER audit_user_roles AFTER INSERT OR UPDATE OR DELETE ON user_roles FOR EACH ROW EXECUTE FUNCTION create_audit_log();

-- Function to calculate invoice totals
CREATE OR REPLACE FUNCTION calculate_invoice_totals()
RETURNS trigger AS $$
DECLARE
  invoice_subtotal decimal(15,2) := 0;
  invoice_tax_total decimal(15,2) := 0;
  invoice_total decimal(15,2) := 0;
BEGIN
  -- Calculate totals from invoice items
  SELECT 
    COALESCE(SUM(line_total), 0),
    COALESCE(SUM(line_total * COALESCE(tr.rate, 0) / 100), 0)
  INTO invoice_subtotal, invoice_tax_total
  FROM invoice_items ii
  LEFT JOIN tax_rates tr ON ii.tax_rate_id = tr.id
  WHERE ii.invoice_id = COALESCE(NEW.invoice_id, OLD.invoice_id);

  invoice_total := invoice_subtotal + invoice_tax_total;

  -- Update invoice totals
  UPDATE invoices 
  SET 
    subtotal = invoice_subtotal,
    tax_total = invoice_tax_total,
    total_amount = invoice_total,
    updated_at = now()
  WHERE id = COALESCE(NEW.invoice_id, OLD.invoice_id);

  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-calculate invoice totals
CREATE TRIGGER calculate_invoice_totals_trigger
  AFTER INSERT OR UPDATE OR DELETE ON invoice_items
  FOR EACH ROW EXECUTE FUNCTION calculate_invoice_totals();

-- Function to calculate line totals for invoice items
CREATE OR REPLACE FUNCTION calculate_line_total()
RETURNS trigger AS $$
BEGIN
  NEW.line_total := NEW.quantity * NEW.unit_price * (1 - COALESCE(NEW.discount_percent, 0) / 100);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-calculate line totals
CREATE TRIGGER calculate_line_total_trigger
  BEFORE INSERT OR UPDATE ON invoice_items
  FOR EACH ROW EXECUTE FUNCTION calculate_line_total();