/*
  # Row Level Security Policies

  1. Tenant-based data isolation
  2. Role-based access control
  3. Super admin access
  4. User management policies
  5. Business data access policies

  Security Features:
  - Multi-tenant data isolation
  - Role-based permissions
  - Audit trail protection
  - User data privacy
*/

-- Helper function to get current user's tenant_id
CREATE OR REPLACE FUNCTION get_user_tenant_id()
R<PERSON><PERSON>NS uuid AS $$
BEGIN
  RETURN (
    SELECT tenant_id 
    FROM users 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has role
CREATE OR REPLACE FUNCTION user_has_role(required_role user_role_type)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM user_roles ur
    JOIN users u ON ur.user_id = u.id
    WHERE u.id = auth.uid() 
    AND ur.role = required_role 
    AND ur.is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has any of the specified roles
CREATE OR REPLACE FUNCTION user_has_any_role(roles user_role_type[])
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM user_roles ur
    JOIN users u ON ur.user_id = u.id
    WHERE u.id = auth.uid() 
    AND ur.role = ANY(roles)
    AND ur.is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- TENANTS POLICIES
CREATE POLICY "Super admins can manage all tenants"
  ON tenants
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));

CREATE POLICY "Users can view their own tenant"
  ON tenants
  FOR SELECT
  TO authenticated
  USING (id = get_user_tenant_id());

CREATE POLICY "Tenant admins can update their tenant"
  ON tenants
  FOR UPDATE
  TO authenticated
  USING (id = get_user_tenant_id() AND user_has_role('tenant_admin'));

-- BILLING PLANS POLICIES
CREATE POLICY "Everyone can view active billing plans"
  ON billing_plans
  FOR SELECT
  TO authenticated
  USING (is_active = true);

CREATE POLICY "Super admins can manage billing plans"
  ON billing_plans
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));

-- TENANT SUBSCRIPTIONS POLICIES
CREATE POLICY "Super admins can manage all subscriptions"
  ON tenant_subscriptions
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));

CREATE POLICY "Users can view their tenant subscription"
  ON tenant_subscriptions
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Tenant admins can update their subscription"
  ON tenant_subscriptions
  FOR UPDATE
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_role('tenant_admin'));

-- USERS POLICIES
CREATE POLICY "Users can view their own profile"
  ON users
  FOR SELECT
  TO authenticated
  USING (id = auth.uid());

CREATE POLICY "Users can update their own profile"
  ON users
  FOR UPDATE
  TO authenticated
  USING (id = auth.uid());

CREATE POLICY "Tenant admins can view users in their tenant"
  ON users
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'manager']::user_role_type[]));

CREATE POLICY "Tenant admins can manage users in their tenant"
  ON users
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_role('tenant_admin'));

CREATE POLICY "Super admins can manage all users"
  ON users
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));

-- USER ROLES POLICIES
CREATE POLICY "Users can view their own roles"
  ON user_roles
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Tenant admins can manage roles in their tenant"
  ON user_roles
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_role('tenant_admin'));

CREATE POLICY "Super admins can manage all roles"
  ON user_roles
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));

-- COMPANIES POLICIES
CREATE POLICY "Users can access companies in their tenant"
  ON companies
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins and managers can manage companies"
  ON companies
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'manager']::user_role_type[]));

-- FISCAL YEARS POLICIES
CREATE POLICY "Users can access fiscal years in their tenant"
  ON fiscal_years
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins and accountants can manage fiscal years"
  ON fiscal_years
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

-- CURRENCIES POLICIES
CREATE POLICY "Everyone can view active currencies"
  ON currencies
  FOR SELECT
  TO authenticated
  USING (is_active = true);

CREATE POLICY "Super admins can manage currencies"
  ON currencies
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));

-- TAX RATES POLICIES
CREATE POLICY "Users can access tax rates in their tenant"
  ON tax_rates
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins and accountants can manage tax rates"
  ON tax_rates
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

-- CHART OF ACCOUNTS POLICIES
CREATE POLICY "Users can access chart of accounts in their tenant"
  ON chart_of_accounts
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins and accountants can manage chart of accounts"
  ON chart_of_accounts
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

-- CUSTOMERS POLICIES
CREATE POLICY "Users can access customers in their tenant"
  ON customers
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Non-viewers can manage customers"
  ON customers
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND NOT user_has_role('viewer'));

-- SUPPLIERS POLICIES
CREATE POLICY "Users can access suppliers in their tenant"
  ON suppliers
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Non-viewers can manage suppliers"
  ON suppliers
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND NOT user_has_role('viewer'));

-- PRODUCTS/SERVICES POLICIES
CREATE POLICY "Users can access products/services in their tenant"
  ON products_services
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Non-viewers can manage products/services"
  ON products_services
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND NOT user_has_role('viewer'));

-- INVOICES POLICIES
CREATE POLICY "Users can access invoices in their tenant"
  ON invoices
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Non-viewers can manage invoices"
  ON invoices
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND NOT user_has_role('viewer'));

-- INVOICE ITEMS POLICIES
CREATE POLICY "Users can access invoice items in their tenant"
  ON invoice_items
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Non-viewers can manage invoice items"
  ON invoice_items
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND NOT user_has_role('viewer'));

-- EXPENSES POLICIES
CREATE POLICY "Users can access expenses in their tenant"
  ON expenses
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Users can create their own expenses"
  ON expenses
  FOR INSERT
  TO authenticated
  WITH CHECK (tenant_id = get_user_tenant_id() AND created_by = auth.uid());

CREATE POLICY "Users can update their own draft expenses"
  ON expenses
  FOR UPDATE
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND created_by = auth.uid() AND status = 'draft');

CREATE POLICY "Managers and admins can manage all expenses"
  ON expenses
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'manager', 'accountant']::user_role_type[]));

-- EXPENSE CATEGORIES POLICIES
CREATE POLICY "Users can access expense categories in their tenant"
  ON expense_categories
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins and accountants can manage expense categories"
  ON expense_categories
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

-- PAYMENTS POLICIES
CREATE POLICY "Users can access payments in their tenant"
  ON payments
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Accountants and admins can manage payments"
  ON payments
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

-- BANK ACCOUNTS POLICIES
CREATE POLICY "Users can access bank accounts in their tenant"
  ON bank_accounts
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins and accountants can manage bank accounts"
  ON bank_accounts
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

-- REPORTS POLICIES
CREATE POLICY "Users can access reports in their tenant"
  ON reports
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND (is_public = true OR created_by = auth.uid()));

CREATE POLICY "Users can manage their own reports"
  ON reports
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND created_by = auth.uid());

CREATE POLICY "Admins can manage all reports"
  ON reports
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_role('tenant_admin'));

-- AUDIT LOGS POLICIES
CREATE POLICY "Admins can view audit logs in their tenant"
  ON audit_logs
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_any_role(ARRAY['tenant_admin', 'accountant']::user_role_type[]));

CREATE POLICY "Super admins can view all audit logs"
  ON audit_logs
  FOR SELECT
  TO authenticated
  USING (user_has_role('super_admin'));

-- NOTIFICATIONS POLICIES
CREATE POLICY "Users can access their own notifications"
  ON notifications
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications"
  ON notifications
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can create notifications"
  ON notifications
  FOR INSERT
  TO authenticated
  WITH CHECK (tenant_id = get_user_tenant_id());

-- SETTINGS POLICIES
CREATE POLICY "Users can view settings in their tenant"
  ON settings
  FOR SELECT
  TO authenticated
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage settings in their tenant"
  ON settings
  FOR ALL
  TO authenticated
  USING (tenant_id = get_user_tenant_id() AND user_has_role('tenant_admin') AND is_system = false);

CREATE POLICY "Super admins can manage system settings"
  ON settings
  FOR ALL
  TO authenticated
  USING (user_has_role('super_admin'));