# Software Requirements Document (SRD)

# Moroccan ERP SaaS Platform

## 1. Introduction

### 1.1 Purpose

This Software Requirements Document (SRD) provides detailed technical specifications for the development of MorERP, a comprehensive ERP solution tailored for Moroccan businesses. This document serves as the technical blueprint for developers, architects, and QA teams.

### 1.2 Scope

The system encompasses a complete business management platform including:

- Multi-tenant SaaS architecture
- Financial management and accounting
- Moroccan tax compliance automation
- Multi-language support (Arabic, French, English)
- Real-time analytics and reporting
- Payment gateway integrations
- Mobile-responsive web application

### 1.3 Document Conventions

- **FR-XXX**: Functional Requirements
- **NFR-XXX**: Non-Functional Requirements
- **API-XXX**: API Requirements
- **DB-XXX**: Database Requirements
- **UI-XXX**: User Interface Requirements

## 2. System Architecture

### 2.1 High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   React + TS    │◄──►│   Supabase      │◄──►│   PostgreSQL    │
│   Tailwind CSS  │    │   Edge Functions│    │   Redis Cache   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN/Storage   │    │   Integrations  │    │   Monitoring    │
│   File Upload   │    │   Payment APIs  │    │   Analytics     │
│   Documents     │    │   Email/SMS     │    │   Logging       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Technology Stack

#### Frontend

- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Context + Custom Hooks
- **Routing**: React Router v6
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Internationalization**: i18next

#### Backend

- **Platform**: Supabase (PostgreSQL + Edge Functions)
- **Authentication**: Supabase Auth
- **Database**: PostgreSQL with Row Level Security
- **File Storage**: Supabase Storage
- **Real-time**: Supabase Realtime
- **API**: RESTful APIs with TypeScript

#### Infrastructure

- **Hosting**: Vercel (Frontend) + Supabase (Backend)
- **CDN**: Vercel Edge Network
- **Monitoring**: Sentry + Supabase Analytics
- **CI/CD**: GitHub Actions

## 3. Functional Requirements

### 3.1 Authentication & Authorization (FR-001)

#### FR-001.1 User Registration

- Email-based registration with verification
- Password strength validation (min 8 chars, uppercase, lowercase, number, special char)
- Optional company information during signup
- Terms of service and privacy policy acceptance
- CAPTCHA protection against bots

#### FR-001.2 User Authentication

- Email/password login
- Multi-factor authentication (MFA) via SMS/Email
- Social login (Google, LinkedIn)
- Password reset via email
- Session management with automatic logout
- Remember me functionality

#### FR-001.3 Role-Based Access Control

- **Owner**: Full system access, billing management
- **Admin**: User management, company settings
- **Accountant**: Financial data access, reporting
- **Employee**: Limited access to assigned modules
- **Viewer**: Read-only access to reports

### 3.2 Company Management (FR-002)

#### FR-002.1 Multi-Tenant Architecture

- Isolated data per tenant/company
- Support for multiple companies per user
- Company switching interface
- Data segregation and security

#### FR-002.2 Company Onboarding

- Step-by-step guided setup wizard
- Company type selection with validation:
  - Auto-entrepreneur (المقاول الذاتي)
  - SARL (شركة ذات المسؤولية المحدودة)
  - SA (شركة مساهمة)
  - SNC (شركة التضامن)
  - SCS (شركة التوصية البسيطة)
- Industry classification with NAF codes
- Automatic tax regime determination
- Regulatory requirements mapping

#### FR-002.3 Company Profile Management

- Company information (name, address, tax ID, etc.)
- Logo upload and branding
- Contact information management
- Legal structure configuration
- Branch/location management

### 3.3 Financial Management (FR-003)

#### FR-003.1 Invoice Management

- Professional invoice creation with templates
- Line item management with products/services
- Automatic tax calculations (VAT, Professional Tax)
- Multi-language invoice generation
- Invoice status tracking (Draft, Sent, Paid, Overdue, Cancelled)
- Recurring invoice setup
- Payment reminder automation
- Invoice PDF generation and email sending

#### FR-003.2 Expense Management

- Expense entry with categorization
- Receipt upload with OCR text extraction
- Expense approval workflows
- Mileage tracking and calculations
- Multi-currency expense support
- Expense reporting and analytics
- Integration with chart of accounts

#### FR-003.3 Customer & Supplier Management

- Contact database with detailed profiles
- Credit limit and payment terms tracking
- Communication history and notes
- Import/export functionality
- Duplicate detection and merging
- Customer/supplier categorization

### 3.4 Moroccan Tax Compliance (FR-004)

#### FR-004.1 Tax Calculations

- VAT calculation with Moroccan rates:
  - Standard rate: 20%
  - Reduced rates: 14%, 10%, 7%
  - Exemptions and special cases
- Professional tax calculation by activity type:
  - Commercial activities: 2.5%
  - Service activities: 2%
  - Professional activities: 1.5%
- Income tax calculation for different business structures
- Social security contributions calculation

#### FR-004.2 Compliance Management

- Automatic deadline tracking for:
  - Monthly VAT declarations
  - Quarterly professional tax
  - Annual income tax
  - Social security contributions
- Compliance calendar with reminders
- Tax report generation in required formats
- Regulatory update notifications
- Audit trail for all transactions

### 3.5 Reporting & Analytics (FR-005)

#### FR-005.1 Financial Reports

- Profit & Loss statements (Moroccan format)
- Balance sheets (Plan Comptable Marocain)
- Cash flow statements
- Trial balance reports
- General ledger
- Accounts receivable/payable aging

#### FR-005.2 Business Analytics

- Revenue and expense trends
- Customer payment behavior analysis
- Product/service performance metrics
- Cash flow forecasting
- Key performance indicators (KPIs)
- Comparative period analysis

#### FR-005.3 Tax Reports

- VAT return reports
- Professional tax declarations
- Income tax summaries
- Social security reports
- Export functionality (PDF, Excel, CSV)

### 3.6 Multi-language Support (FR-006)

#### FR-006.1 Internationalization

- Interface translation (Arabic, French, English)
- RTL (Right-to-Left) layout for Arabic
- Cultural date and number formatting
- Currency localization (MAD primary)
- Time zone support

#### FR-006.2 Document Localization

- Multi-language invoice templates
- Localized report formats
- Cultural business practices adaptation
- Legal text translations

## 4. Non-Functional Requirements

### 4.1 Performance (NFR-001)

- Page load time: < 2 seconds (95th percentile)
- API response time: < 500ms (average)
- Database query optimization
- Lazy loading for large datasets
- Caching strategy implementation
- CDN utilization for static assets

### 4.2 Scalability (NFR-002)

- Support for 10,000+ concurrent users
- Horizontal scaling capability
- Auto-scaling based on demand
- Database partitioning for large datasets
- Microservices architecture readiness

### 4.3 Availability (NFR-003)

- 99.9% uptime SLA
- Automated failover mechanisms
- Regular backup procedures (daily)
- Disaster recovery plan
- Health monitoring and alerting

### 4.4 Security (NFR-004)

- End-to-end encryption for sensitive data
- HTTPS enforcement
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting on APIs
- Regular security audits
- GDPR compliance
- Data anonymization capabilities

### 4.5 Usability (NFR-005)

- Mobile-responsive design (320px to 4K)
- Accessibility compliance (WCAG 2.1 AA)
- Keyboard navigation support
- Screen reader compatibility
- Intuitive user interface
- Consistent design patterns
- Progressive web app capabilities

## 5. Database Requirements

### 5.1 Core Tables (DB-001)

```sql
-- Multi-tenancy
tenants (id, name, slug, settings, created_at, updated_at)
tenant_subscriptions (id, tenant_id, plan_id, status, billing_cycle)

-- User Management
users (id, email, encrypted_password, role, tenant_id, profile_data)
user_sessions (id, user_id, token, expires_at, created_at)

-- Company Management
companies (id, tenant_id, name, tax_id, company_type, industry_code)
company_settings (id, company_id, settings_json, updated_at)

-- Financial Data
invoices (id, company_id, customer_id, number, status, total, tax_amount)
invoice_items (id, invoice_id, description, quantity, unit_price, tax_rate)
expenses (id, company_id, category_id, amount, description, receipt_url)
customers (id, company_id, name, email, address, payment_terms)
suppliers (id, company_id, name, email, address, payment_terms)

-- Tax & Compliance
tax_rates (id, country, region, type, rate, effective_date)
tax_obligations (id, company_id, type, period, amount, due_date, status)
compliance_deadlines (id, company_id, type, due_date, description)
```

### 5.2 Data Relationships (DB-002)

- One-to-many: Tenant → Companies → Invoices/Expenses
- Many-to-many: Users ↔ Companies (with roles)
- Foreign key constraints with cascading deletes
- Indexes on frequently queried columns
- Full-text search indexes for names and descriptions

### 5.3 Data Security (DB-003)

- Row Level Security (RLS) policies
- Encrypted sensitive fields (tax IDs, bank details)
- Audit logging for all data changes
- Data retention policies
- Backup encryption

## 6. API Requirements

### 6.1 RESTful API Design (API-001)

```
Authentication:
POST /auth/login
POST /auth/register
POST /auth/logout
POST /auth/refresh
POST /auth/forgot-password
POST /auth/reset-password

Companies:
GET /api/companies
POST /api/companies
GET /api/companies/{id}
PUT /api/companies/{id}
DELETE /api/companies/{id}

Invoices:
GET /api/invoices
POST /api/invoices
GET /api/invoices/{id}
PUT /api/invoices/{id}
DELETE /api/invoices/{id}
POST /api/invoices/{id}/send
POST /api/invoices/{id}/payment

Expenses:
GET /api/expenses
POST /api/expenses
GET /api/expenses/{id}
PUT /api/expenses/{id}
DELETE /api/expenses/{id}
POST /api/expenses/{id}/receipt

Reports:
GET /api/reports/financial
GET /api/reports/tax
GET /api/reports/analytics
POST /api/reports/export

Tax Compliance:
GET /api/tax/rates
GET /api/tax/obligations
POST /api/tax/calculate
GET /api/compliance/deadlines
```

### 6.2 API Security (API-002)

- JWT token authentication
- API rate limiting (100 requests/minute per user)
- Request/response validation
- CORS configuration
- API versioning (/api/v1/)
- Error handling with proper HTTP status codes

### 6.3 Real-time Features (API-003)

- WebSocket connections for live updates
- Invoice status changes
- Payment notifications
- System alerts and reminders
- Collaborative editing capabilities

## 7. User Interface Requirements

### 7.1 Design System (UI-001)

#### Color Palette

- **Primary**: Blue (#3B82F6, #1D4ED8, #1E40AF)
- **Secondary**: Gray (#6B7280, #374151, #111827)
- **Success**: Green (#10B981, #059669)
- **Warning**: Yellow (#F59E0B, #D97706)
- **Error**: Red (#EF4444, #DC2626)
- **Background**: White (#FFFFFF) / Dark (#111827)

#### Typography

- **Primary Font**: Inter (Latin scripts)
- **Arabic Font**: Noto Sans Arabic
- **Font Sizes**: 12px, 14px, 16px, 18px, 20px, 24px, 32px, 48px
- **Font Weights**: 400 (Regular), 500 (Medium), 600 (Semibold), 700 (Bold)

#### Spacing System

- **Base Unit**: 4px
- **Scale**: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px, 48px, 64px

### 7.2 Responsive Design (UI-002)

#### Breakpoints

- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px - 1439px
- **Large Desktop**: 1440px+

#### Layout Patterns

- Mobile-first responsive design
- Flexible grid system (CSS Grid + Flexbox)
- Collapsible navigation for mobile
- Touch-friendly interface elements (min 44px touch targets)

### 7.3 Accessibility (UI-003)

- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus indicators for all interactive elements
- Alternative text for images
- Semantic HTML structure

### 7.4 RTL Support (UI-004)

- Automatic layout mirroring for Arabic
- Text direction switching
- Icon and image positioning adjustments
- Date picker and form field adaptations
- Navigation menu RTL layout

## 8. Integration Requirements

### 8.1 Payment Gateway Integration (INT-001)

#### CMI (Centre Monétique Interbancaire)

```javascript
// CMI Integration Specifications
{
  merchantId: "string",
  key: "string",
  siteId: "string",
  hashKey: "string",
  currency: "MAD",
  supportedCards: ["VISA", "MASTERCARD", "CMI"],
  testMode: boolean,
  webhookUrl: "string",
  returnUrl: "string",
  cancelUrl: "string"
}
```

#### Payzone Integration

```javascript
// Payzone Integration Specifications
{
  merchantId: "string",
  apiKey: "string",
  apiSecret: "string",
  integrationId: "string",
  terminalId: "string",
  currency: "MAD",
  supportedCards: ["VISA", "MASTERCARD", "AMEX"],
  tokenization: boolean,
  recurringPayments: boolean,
  webhookUrl: "string"
}
```

### 8.2 Email Service Integration (INT-002)

- **Primary**: SendGrid for transactional emails
- **Backup**: AWS SES
- **Templates**: Invoice delivery, payment reminders, notifications
- **Localization**: Multi-language email templates
- **Tracking**: Open rates, click rates, delivery status

### 8.3 SMS Gateway Integration (INT-003)

- **Provider**: Twilio or local Moroccan SMS provider
- **Use Cases**: MFA codes, payment alerts, deadline reminders
- **Languages**: Arabic, French, English
- **Rate Limiting**: Prevent SMS spam

### 8.4 File Storage Integration (INT-004)

- **Primary**: Supabase Storage
- **File Types**: PDF, JPG, PNG, DOC, XLS (max 10MB)
- **Security**: Virus scanning, access control
- **CDN**: Global content delivery
- **Backup**: Automated daily backups

## 9. Moroccan Business Logic

### 9.1 Company Types & Regulations (BL-001)

#### Auto-Entrepreneur (المقاول الذاتي)

```javascript
{
  revenueLimit: {
    services: 500000, // MAD
    commerce: 1000000 // MAD
  },
  taxRegime: "simplified",
  vatThreshold: 150000, // MAD monthly
  socialSecurity: "voluntary",
  accountingRequirements: "simplified"
}
```

#### SARL (شركة ذات المسؤولية المحدودة)

```javascript
{
  minCapital: 10000, // MAD
  maxShareholders: 50,
  minShareholders: 1,
  liability: "limited",
  taxRegime: ["normal", "simplified"],
  vatMandatory: true,
  auditRequired: false,
  annualFilings: ["financial_statements", "tax_returns"]
}
```

#### SA (شركة مساهمة)

```javascript
{
  minCapital: 300000, // MAD
  minShareholders: 5,
  liability: "limited",
  taxRegime: "normal",
  boardRequired: true,
  auditRequired: true,
  quarterlyReporting: true,
  annualFilings: ["audited_statements", "board_reports"]
}
```

### 9.2 Tax Calculation Logic (BL-002)

#### VAT Calculation

```javascript
function calculateVAT(amount, rate, companyType, activityType) {
  const vatRates = {
    standard: 0.2,
    reduced_1: 0.14,
    reduced_2: 0.1,
    reduced_3: 0.07,
    exempt: 0.0,
  };

  // Auto-entrepreneur exemption logic
  if (companyType === "auto_entrepreneur") {
    const monthlyRevenue = getCurrentMonthRevenue();
    if (monthlyRevenue < 150000) return 0;
  }

  return amount * vatRates[rate];
}
```

#### Professional Tax Calculation

```javascript
function calculateProfessionalTax(revenue, activityType) {
  const rates = {
    commercial: 0.025,
    services: 0.02,
    professional: 0.015,
  };

  const baseAmount = Math.max(revenue - 10000, 0);
  const tax = baseAmount * rates[activityType];

  return Math.max(tax, 12000); // Minimum annual tax
}
```

### 9.3 Compliance Deadlines (BL-003)

```javascript
const complianceCalendar = {
  vat: {
    frequency: "monthly",
    deadline: "20th of following month",
    forms: ["VAT_RETURN", "VAT_PAYMENT"],
  },
  professionalTax: {
    frequency: "quarterly",
    deadline: "End of month following quarter",
    forms: ["PROF_TAX_DECLARATION"],
  },
  incomeTax: {
    frequency: "annual",
    deadline: "March 31st",
    forms: ["INCOME_TAX_RETURN"],
  },
  socialSecurity: {
    frequency: "monthly",
    deadline: "15th of following month",
    forms: ["SS_DECLARATION", "SS_PAYMENT"],
  },
};
```

## 10. Testing Requirements

### 10.1 Unit Testing (TEST-001)

- **Framework**: Jest + React Testing Library
- **Coverage**: Minimum 80% code coverage
- **Components**: All React components
- **Utilities**: Business logic functions
- **Hooks**: Custom React hooks
- **API**: Service layer functions

### 10.2 Integration Testing (TEST-002)

- **API Testing**: All endpoints with various scenarios
- **Database**: CRUD operations and constraints
- **Authentication**: Login flows and permissions
- **Payment**: Gateway integration testing
- **Email/SMS**: Service integration testing

### 10.3 End-to-End Testing (TEST-003)

- **Framework**: Cypress or Playwright
- **User Flows**: Complete business processes
- **Cross-browser**: Chrome, Firefox, Safari, Edge
- **Mobile**: Responsive design testing
- **Performance**: Load time and interaction testing

### 10.4 Security Testing (TEST-004)

- **Penetration Testing**: Quarterly security audits
- **Vulnerability Scanning**: Automated tools
- **Authentication**: MFA and session management
- **Authorization**: Role-based access control
- **Data Protection**: Encryption and privacy

## 11. Deployment & DevOps

### 11.1 CI/CD Pipeline (DEV-001)

```yaml
# GitHub Actions Workflow
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Tests
        run: npm test
      - name: Run E2E Tests
        run: npm run test:e2e

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
      - name: Run Database Migrations
        run: npm run db:migrate
```

### 11.2 Environment Configuration (DEV-002)

#### Development

- Local Supabase instance
- Test payment gateways
- Debug logging enabled
- Hot reloading

#### Staging

- Staging Supabase project
- Sandbox payment gateways
- Production-like data
- Performance monitoring

#### Production

- Production Supabase project
- Live payment gateways
- Error tracking (Sentry)
- Performance monitoring
- Automated backups

### 11.3 Monitoring & Alerting (DEV-003)

- **Application Monitoring**: Sentry for error tracking
- **Performance Monitoring**: Vercel Analytics
- **Database Monitoring**: Supabase metrics
- **Uptime Monitoring**: External service (Pingdom)
- **Alert Channels**: Email, Slack, SMS

## 12. Data Migration & Import

### 12.1 Data Import Formats (DATA-001)

- **CSV Import**: Customers, suppliers, products, expenses
- **Excel Import**: Financial data with templates
- **API Import**: From existing accounting software
- **Manual Entry**: Guided data entry forms

### 12.2 Data Validation (DATA-002)

- **Format Validation**: Email, phone, tax ID formats
- **Business Rules**: Moroccan tax ID validation
- **Duplicate Detection**: Fuzzy matching algorithms
- **Data Cleansing**: Automatic formatting and correction

### 12.3 Migration Tools (DATA-003)

- **Import Wizard**: Step-by-step data import
- **Mapping Interface**: Field mapping for different formats
- **Preview & Validation**: Data preview before import
- **Error Handling**: Detailed error reports and correction

This comprehensive SRD provides developers with all necessary technical specifications to build a robust, compliant, and user-friendly ERP solution for Moroccan businesses.
